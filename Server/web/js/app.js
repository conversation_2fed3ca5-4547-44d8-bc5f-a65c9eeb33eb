// Desktop Shake & Match App JavaScript
class ShakeMatchApp {
    constructor() {
        this.socket = null;
        this.user = null;
        this.userProfile = null;
        this.matches = [];
        this.currentChat = null;
        this.premiumStatus = null;
        // Use the same server address as the mobile app
        this.serverAddress = 'http://**************:3000';

        console.log('Web app will connect to:', this.serverAddress);
        this.init();
    }

    async init() {
        console.log('App initializing...');

        // Test server connection first
        await this.testServerConnection();

        // Check if user is already logged in
        const token = localStorage.getItem('authToken');
        if (token) {
            try {
                await this.validateToken(token);
                this.showMainScreen();
            } catch (error) {
                console.error('Token validation failed:', error);
                localStorage.removeItem('authToken');
                this.showLoginScreen();
            }
        } else {
            console.log('No token found, showing login screen');
            this.showLoginScreen();
        }

        // Set up keyboard shortcuts
        this.setupKeyboardShortcuts();

        // Set up click outside handler for menus
        this.setupClickOutsideHandler();

        // Set up socket connection if logged in
        if (this.user) {
            this.initializeSocket();
        }
    }

    async testServerConnection() {
        try {
            console.log('=== TESTING SERVER CONNECTION ===');
            console.log(`Testing connection to: ${this.serverAddress}`);

            const response = await fetch(`${this.serverAddress}/api/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Server connection successful:', data);
            } else {
                console.error('❌ Server connection failed:', response.status);
            }
        } catch (error) {
            console.error('❌ Server connection error:', error);
        }
    }

    showLoginScreen() {
        console.log('Showing login screen');
        const loginScreen = document.getElementById('login-screen');
        const mainScreen = document.getElementById('main-screen');

        loginScreen.classList.add('active');
        mainScreen.classList.remove('active');

        // Debug login screen visibility
        console.log('Login screen classes:', loginScreen.className);
        const computedStyles = window.getComputedStyle(loginScreen);
        console.log('Login screen computed opacity:', computedStyles.opacity);
        console.log('Login screen computed visibility:', computedStyles.visibility);
        console.log('Login screen computed display:', computedStyles.display);
    }

    showMainScreen() {
        console.log('Showing main screen');
        const loginScreen = document.getElementById('login-screen');
        const mainScreen = document.getElementById('main-screen');

        loginScreen.classList.remove('active');
        mainScreen.classList.add('active');
        this.showView('home');
        this.updateWelcomeText();
        this.updateProfileIcon();
        this.loadMatches();
        this.loadPremiumStatus();
    }

    async validateToken(token) {
        const response = await fetch(`${this.serverAddress}/api/validate-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Token validation failed');
        }

        const data = await response.json();
        this.user = data.user;
        this.userProfile = data.profile;
        this.updateWelcomeText();
        this.updateProfileIcon();
        return data;
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Spacebar for shake (when on home view)
            if (e.code === 'Space' && this.getCurrentView() === 'home') {
                e.preventDefault();
                this.handleShake();
            }
            
            // Escape to go back
            if (e.code === 'Escape') {
                const currentView = this.getCurrentView();
                if (currentView !== 'home') {
                    this.showView('home');
                }
            }
        });
    }

    setupClickOutsideHandler() {
        // Close match options menus and settings dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.match-options-container')) {
                document.querySelectorAll('.match-options-menu').forEach(menu => {
                    menu.style.display = 'none';
                });
            }

            // Close settings dropdown when clicking outside
            if (!e.target.closest('.settings-dropdown-container')) {
                const dropdown = document.getElementById('settings-dropdown');
                if (dropdown) {
                    dropdown.classList.remove('active');
                }
            }
        });
    }

    getCurrentView() {
        const activeView = document.querySelector('.view.active');
        return activeView ? activeView.id.replace('-view', '') : 'home';
    }

    showView(viewName) {
        // Hide all views
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });

        // Show selected view
        document.getElementById(`${viewName}-view`).classList.add('active');

        // Initialize view-specific content
        if (viewName === 'profile') {
            this.initializeProfileView();
        } else if (viewName === 'settings') {
            this.initializeSettingsView();
        }
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            this.showAlert('Please enter both username and password');
            return;
        }

        try {
            const response = await fetch(`${this.serverAddress}/api/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok) {
                localStorage.setItem('authToken', data.token);
                this.user = data.user;
                this.userProfile = data.profile;

                console.log('=== LOGIN SUCCESS ===');
                console.log('User data:', JSON.stringify(this.user, null, 2));
                console.log('User ID:', this.user.id);
                console.log('User ID type:', typeof this.user.id);
                console.log('Profile data:', JSON.stringify(this.userProfile, null, 2));

                this.updateWelcomeText();
                this.updateProfileIcon();
                this.initializeSocket();
                this.showMainScreen();
            } else {
                this.showAlert(data.error || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('Network error. Please try again.');
        }
    }

    async handleSocialLogin(provider) {
        // For now, show a message that social login is not implemented in desktop
        this.showAlert(`${provider} login will be implemented soon for desktop`);
    }

    initializeSocket() {
        if (this.socket) {
            this.socket.disconnect();
        }

        this.socket = io(this.serverAddress);
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            if (this.user) {
                this.socket.emit('register', {
                    userId: this.user.id,
                    username: this.user.username
                });
            }
        });

        this.socket.on('new-match', (data) => {
            console.log('New match received:', data);
            this.matches.push(data);
            this.renderMatches();
            this.showNotification('New match found!');
        });

        // Handle shake response when no match is found
        this.socket.on('shake-response', (data) => {
            console.log('Shake response received:', data);
            if (data.success && !data.match) {
                this.showNotification('No matches found nearby. Try again later!');
            } else if (!data.success) {
                this.showAlert(data.error || 'Shake failed');
            }
        });

        this.socket.on('message', (data) => {
            console.log('New message received:', data);
            if (this.currentChat && this.currentChat.userId === data.senderId) {
                this.addMessageToChat(data);
            }
            this.showNotification(`New message from ${data.senderUsername}`);
        });

        // Listen for unread messages (same as mobile app)
        this.socket.on('unreadMessages', (messages) => {
            console.log('=== RECEIVED UNREAD MESSAGES ===');
            console.log('Messages from server:', messages);
            console.log('Number of messages received:', messages.length);

            if (messages.length > 0) {
                console.log('Sample message structure:', JSON.stringify(messages[0], null, 2));

                // Log all message sender/receiver IDs for debugging
                messages.forEach((msg, index) => {
                    console.log(`Message ${index + 1}:`, {
                        senderId: msg.senderId,
                        senderIdType: typeof msg.senderId,
                        receiverId: msg.receiverId,
                        receiverIdType: typeof msg.receiverId,
                        text: msg.text?.substring(0, 20) + '...'
                    });
                });
            }

            if (this.currentChat && messages.length > 0) {
                const currentUserIdStr = this.user.id.toString();
                const targetUserIdStr = this.currentChat.userId.toString();

                console.log('Filtering criteria:');
                console.log('Current user ID:', currentUserIdStr);
                console.log('Target user ID:', targetUserIdStr);

                // Filter messages for current conversation
                // Since receiverId is missing from server response, filter by senderId only
                // Messages should be from either the current user or the target user
                const conversationMessages = messages.filter(msg => {
                    const senderIdStr = msg.senderId ? msg.senderId.toString() : '';

                    const isFromCurrentUser = (senderIdStr === currentUserIdStr);
                    const isFromTargetUser = (senderIdStr === targetUserIdStr);

                    console.log(`Message check: sender=${senderIdStr}, fromCurrentUser=${isFromCurrentUser}, fromTargetUser=${isFromTargetUser}`);

                    // Include message if it's from either user in this conversation
                    return isFromCurrentUser || isFromTargetUser;
                });

                console.log(`Found ${conversationMessages.length} messages for current conversation`);

                if (conversationMessages.length > 0) {
                    console.log('Conversation messages:', conversationMessages);
                }

                this.renderChatMessages(conversationMessages);
            } else if (messages.length > 0) {
                console.log('No current chat or no messages to filter');
                // If no current chat, just render all messages (shouldn't happen but for debugging)
                this.renderChatMessages(messages);
            }
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
        });
    }

    async handleShake() {
        if (!this.user || !this.user.id) {
            console.log('Shake ignored - missing user data');
            return;
        }

        const shakeBtn = document.getElementById('shake-btn');
        const searchingBubble = document.getElementById('searching-bubble');

        // Check if already shaking
        if (shakeBtn.disabled) {
            console.log('Shake ignored - already shaking');
            return;
        }

        shakeBtn.classList.add('shaking');
        shakeBtn.disabled = true;
        searchingBubble.style.display = 'block';

        try {
            // Get user's location
            const location = await this.getCurrentLocation();

            // Get user preferences from localStorage
            const maxDistance = parseInt(localStorage.getItem('maxDistance') || '25');
            const minAge = parseInt(localStorage.getItem('minAge') || '18');
            const maxAge = parseInt(localStorage.getItem('maxAge') || '100');
            const blockedUsers = JSON.parse(localStorage.getItem('blockedUsers') || '[]');

            console.log('Shake initiated by user:', this.user.username, 'ID:', this.user.id);
            console.log('Location:', location);
            console.log('Preferences:', { maxDistance, minAge, maxAge });

            // Use socket.io like the mobile app instead of REST API
            if (this.socket && this.socket.connected) {
                console.log('Sending shake event to server via socket');
                this.socket.emit('shake', {
                    userId: this.user.id,
                    username: this.user.username,
                    location: location,
                    maxDistance: maxDistance,
                    timestamp: new Date().toISOString(),
                    blockedUsers: blockedUsers,
                    rematchEnabled: true,
                    minAge: minAge,
                    maxAge: maxAge
                });

                console.log(`Shake event sent with userID: ${this.user.id}, username: ${this.user.username}, location:`, JSON.stringify(location));
            } else {
                console.log('Cannot send shake event - socket not connected');
                this.showAlert('Connection error. Please refresh the page and try again.');
                return;
            }

        } catch (error) {
            console.error('Shake error:', error);
            this.showAlert('Failed to shake. Please check your location permissions and try again.');
        } finally {
            // Reset UI after 3 seconds (same as mobile app)
            setTimeout(() => {
                shakeBtn.classList.remove('shaking');
                shakeBtn.disabled = false;
                searchingBubble.style.display = 'none';
            }, 3000);
        }
    }

    async getCurrentLocation() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                // Fallback location (you might want to ask user to set this)
                resolve({ latitude: 40.7128, longitude: -74.0060 }); // NYC default
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    resolve({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude
                    });
                },
                (error) => {
                    console.error('Geolocation error:', error);
                    // Fallback location
                    resolve({ latitude: 40.7128, longitude: -74.0060 });
                }
            );
        });
    }

    async loadMatches() {
        if (!this.user) return;

        try {
            const response = await fetch(`${this.serverAddress}/api/matches`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.matches = data.matches || [];
                this.renderMatches();
            }
        } catch (error) {
            console.error('Failed to load matches:', error);
        }
    }

    renderMatches() {
        const matchesList = document.getElementById('matches-list');

        if (this.matches.length === 0) {
            matchesList.innerHTML = `
                <div class="no-matches">
                    <svg class="no-matches-icon"><use href="#icon-heart"/></svg>
                    <p>No matches yet.</p>
                </div>
            `;
            return;
        }

        matchesList.innerHTML = this.matches.map(match => `
            <div class="match-card" data-user-id="${match.userId}">
                <div class="match-avatar" onclick="app.viewProfile('${match.userId}')">
                    ${match.profileImage ?
                        `<img src="data:image/jpeg;base64,${match.profileImage}" alt="${match.username}">` :
                        match.username.charAt(0).toUpperCase()
                    }
                </div>
                <div class="match-info" onclick="app.viewProfile('${match.userId}')">
                    <div class="match-name-row">
                        <h3>${match.username}</h3>
                        ${match.matchType === 'friends' ?
                            '<span class="friend-badge"><svg><use href="#icon-people"/></svg>Friend</span>' :
                            ''
                        }
                    </div>
                    <p class="match-distance">${Math.round(match.distance * 10) / 10} km away</p>
                </div>
                <div class="match-actions">
                    <button class="chat-button" onclick="app.openChat('${match.userId}', '${match.username}')" title="Chat">
                        <svg><use href="#icon-chat"/></svg>
                    </button>
                    <div class="match-options-container">
                        <button class="match-options-btn" onclick="app.toggleMatchOptions('${match.userId}')" title="More options">
                            <svg><use href="#icon-more"/></svg>
                        </button>
                        <div class="match-options-menu" id="match-options-${match.userId}" style="display: none;">
                            <button class="match-option-item" onclick="app.viewProfile('${match.userId}')">
                                <svg><use href="#icon-user"/></svg>
                                <span>View Profile</span>
                            </button>
                            <button class="match-option-item danger" onclick="app.blockUser('${match.userId}', '${match.username}')">
                                <svg><use href="#icon-close"/></svg>
                                <span>Block User</span>
                            </button>
                            <button class="match-option-item danger" onclick="app.deleteMatch('${match.userId}', '${match.username}')">
                                <svg><use href="#icon-close"/></svg>
                                <span>Delete Match</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    toggleMatchOptions(userId) {
        // Close all other open menus first
        document.querySelectorAll('.match-options-menu').forEach(menu => {
            if (menu.id !== `match-options-${userId}`) {
                menu.style.display = 'none';
            }
        });

        // Toggle the clicked menu
        const menu = document.getElementById(`match-options-${userId}`);
        if (menu) {
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        }
    }

    async deleteMatch(userId, username) {
        if (!confirm(`Are you sure you want to delete your match with ${username}? This action cannot be undone.`)) {
            return;
        }

        try {
            const response = await fetch(`${this.serverAddress}/api/matches/${userId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                // Remove from local matches array
                this.matches = this.matches.filter(match => match.userId !== userId);
                this.renderMatches();
                this.showNotification(`Match with ${username} deleted`);

                // Close the options menu
                const menu = document.getElementById(`match-options-${userId}`);
                if (menu) menu.style.display = 'none';
            } else {
                const data = await response.json();
                this.showAlert(data.error || 'Failed to delete match');
            }
        } catch (error) {
            console.error('Error deleting match:', error);
            this.showAlert('Failed to delete match. Please try again.');
        }
    }

    async blockUser(userId, username) {
        if (!confirm(`Are you sure you want to block ${username}? You won't see each other in matches anymore.`)) {
            return;
        }

        try {
            const response = await fetch(`${this.serverAddress}/api/users/block`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({ userId })
            });

            if (response.ok) {
                // Remove from local matches array
                this.matches = this.matches.filter(match => match.userId !== userId);
                this.renderMatches();
                this.showNotification(`${username} has been blocked`);

                // Update blocked users in localStorage
                const blockedUsers = JSON.parse(localStorage.getItem('blockedUsers') || '[]');
                if (!blockedUsers.includes(userId)) {
                    blockedUsers.push(userId);
                    localStorage.setItem('blockedUsers', JSON.stringify(blockedUsers));
                }

                // Close the options menu
                const menu = document.getElementById(`match-options-${userId}`);
                if (menu) menu.style.display = 'none';
            } else {
                const data = await response.json();
                this.showAlert(data.error || 'Failed to block user');
            }
        } catch (error) {
            console.error('Error blocking user:', error);
            this.showAlert('Failed to block user. Please try again.');
        }
    }

    openChat(userId, username) {
        console.log('=== OPENING CHAT ===');
        console.log(`Target user ID: ${userId} (type: ${typeof userId})`);
        console.log(`Target username: ${username}`);
        console.log(`Current user ID: ${this.user.id} (type: ${typeof this.user.id})`);

        this.currentChat = { userId, username };
        this.showView('chat');

        // Update chat header first
        document.getElementById('chat-username').textContent = username;
        const chatAvatar = document.getElementById('chat-avatar');
        const match = this.matches.find(m => m.userId === userId);
        if (match && match.profileImage) {
            chatAvatar.innerHTML = `<img src="data:image/jpeg;base64,${match.profileImage}" alt="${username}">`;
        } else {
            chatAvatar.textContent = username.charAt(0).toUpperCase();
        }

        // Show loading state
        const chatMessages = document.getElementById('chat-messages');
        chatMessages.innerHTML = `
            <div class="chat-loading-state">
                <p>Loading messages...</p>
            </div>
        `;

        // Load existing messages
        this.loadChatMessages(userId);
    }

    loadChatMessages(userId) {
        console.log('=== LOADING CHAT MESSAGES (SOCKET METHOD) ===');
        console.log(`Target user ID: ${userId}`);
        console.log(`Current user ID: ${this.user.id}`);

        // Use the same method as mobile app - socket event
        if (this.socket && this.socket.connected) {
            console.log('Requesting messages via socket (same as mobile app)');

            // Use the same socket event as mobile app
            this.socket.emit('getUnreadMessages', {
                userId: this.user.id.toString(),
                otherUserId: userId.toString()
            });

            console.log('Socket request sent for getUnreadMessages');
        } else {
            console.error('Socket not connected, cannot load messages');
            this.renderChatMessages([]);
        }
    }

    renderChatMessages(messages) {
        const chatMessages = document.getElementById('chat-messages');

        if (messages.length === 0) {
            chatMessages.innerHTML = `
                <div class="chat-empty-state">
                    <p>Start your conversation!</p>
                </div>
            `;
            return;
        }

        chatMessages.innerHTML = messages.map(message => {
            // Handle both senderId (string) and senderId (ObjectId) formats
            const isOwnMessage = message.senderId === this.user.id || message.senderId.toString() === this.user.id;
            return `
                <div class="message ${isOwnMessage ? 'own' : 'other'}">
                    <div class="message-bubble">
                        ${message.text}
                        <span class="message-time">${this.formatTime(message.timestamp)}</span>
                    </div>
                </div>
            `;
        }).join('');

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    showAlert(message) {
        // Simple alert for now - you could implement a custom modal
        alert(message);
    }

    showNotification(message) {
        // Simple notification - you could implement a toast system
        console.log('Notification:', message);

        // For now, just show a temporary message
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    addMessageToChat(message) {
        const chatMessages = document.getElementById('chat-messages');

        // Remove empty state if it exists
        const emptyState = chatMessages.querySelector('.chat-empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        const messageElement = document.createElement('div');
        // Handle both senderId (string) and senderId (ObjectId) formats
        const isOwnMessage = message.senderId === this.user.id || message.senderId.toString() === this.user.id;
        messageElement.className = `message ${isOwnMessage ? 'own' : 'other'}`;
        messageElement.innerHTML = `
            <div class="message-bubble">
                ${message.text}
                <span class="message-time">${this.formatTime(message.timestamp)}</span>
            </div>
        `;
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    async viewProfile(userId) {
        const match = this.matches.find(m => m.userId === userId);
        if (!match) {
            this.showAlert('User not found');
            return;
        }

        this.currentProfileUser = match;
        this.currentProfileImages = [];
        this.currentImageIndex = 0;

        // Show modal
        const modal = document.getElementById('profile-modal');
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('active'), 10);

        // Set basic info
        document.getElementById('profile-modal-title').textContent = match.username;
        document.getElementById('profile-modal-name').textContent = match.username;

        // Load profile data
        await this.loadUserProfile(userId, match.username);
    }

    async loadUserProfile(userId, username) {
        try {
            const response = await fetch(`${this.serverAddress}/api/profile/${userId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displayProfileData(data.profile, username);
            } else {
                // Show basic profile with just username
                this.displayProfileData({ username }, username);
            }
        } catch (error) {
            console.error('Error loading profile:', error);
            this.displayProfileData({ username }, username);
        }
    }

    displayProfileData(profile, username) {
        // Update name with age if available
        const nameText = profile.age ? `${username}, ${profile.age}` : username;
        document.getElementById('profile-modal-name').textContent = nameText;

        // Handle images
        const photoContainer = document.getElementById('profile-photo-container');
        const photoNav = document.getElementById('photo-nav');

        if (profile.images && profile.images.length > 0) {
            this.currentProfileImages = profile.images;
            this.currentImageIndex = 0;
            this.displayProfileImage(0);

            if (profile.images.length > 1) {
                photoNav.style.display = 'flex';
                this.updatePhotoIndicators();
                console.log('Showing photo nav for', profile.images.length, 'images');
            } else {
                photoNav.style.display = 'none';
            }
        } else {
            // Show avatar - reset photo container to show placeholder
            photoContainer.innerHTML = `
                <div class="profile-photo-placeholder" id="profile-photo-placeholder">
                    <div class="profile-avatar-large" id="profile-avatar-large">
                        <span id="profile-avatar-text">${username.charAt(0).toUpperCase()}</span>
                    </div>
                </div>
            `;
            photoNav.style.display = 'none';
            this.currentProfileImages = [];
        }

        // Handle description
        const descriptionSection = document.getElementById('profile-description-section');
        const descriptionText = document.getElementById('profile-modal-description');

        if (profile.description) {
            descriptionText.textContent = profile.description;
            descriptionSection.style.display = 'block';
        } else {
            descriptionText.textContent = 'No description available';
            descriptionSection.style.display = 'block';
        }

        // Handle passions
        const passionsSection = document.getElementById('profile-passions-section');
        const passionTags = document.getElementById('profile-passion-tags');

        if (profile.passions && profile.passions.length > 0) {
            passionTags.innerHTML = profile.passions.map(passion =>
                `<span class="profile-passion-tag">${passion}</span>`
            ).join('');
            passionsSection.style.display = 'block';
        } else {
            passionsSection.style.display = 'none';
        }
    }

    displayProfileImage(index) {
        if (!this.currentProfileImages || this.currentProfileImages.length === 0) return;

        const photoContainer = document.getElementById('profile-photo-container');
        const image = this.currentProfileImages[index];
        const imageUri = image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}`;

        photoContainer.innerHTML = `<img src="${imageUri}" class="profile-photo" alt="Profile photo">`;
        this.currentImageIndex = index;
        this.updatePhotoIndicators();
    }

    updatePhotoIndicators() {
        const indicators = document.getElementById('photo-indicators');
        if (!this.currentProfileImages || this.currentProfileImages.length <= 1) {
            indicators.innerHTML = '';
            return;
        }

        indicators.innerHTML = this.currentProfileImages.map((_, index) =>
            `<div class="photo-indicator ${index === this.currentImageIndex ? 'active' : ''}" onclick="app.displayProfileImage(${index})"></div>`
        ).join('');
    }

    updateWelcomeText() {
        const welcomeText = document.getElementById('welcome-text');
        if (welcomeText && this.user) {
            welcomeText.textContent = `Hi, ${this.user.username}!`;
        }
    }

    updateProfileIcon() {
        const profileBtn = document.getElementById('profile-btn');
        const profileIconContainer = profileBtn.querySelector('.profile-icon-container');

        if (!profileIconContainer) return;

        // Check if user has profile image
        const hasProfileImage = this.userProfile && this.userProfile.images && this.userProfile.images.length > 0;

        if (hasProfileImage) {
            const imageUri = this.userProfile.images[0].startsWith('data:')
                ? this.userProfile.images[0]
                : `data:image/jpeg;base64,${this.userProfile.images[0]}`;

            profileIconContainer.innerHTML = `<img src="${imageUri}" alt="Profile" class="profile-icon-image">`;
        } else {
            profileIconContainer.innerHTML = `<svg class="nav-icon default-profile-icon"><use href="#icon-user"/></svg>`;
        }
    }

    initializeProfileView() {
        // Populate age dropdown
        const ageSelect = document.getElementById('profile-age');
        if (ageSelect && ageSelect.children.length <= 1) {
            for (let age = 18; age <= 120; age++) {
                const option = document.createElement('option');
                option.value = age;
                option.textContent = age;
                ageSelect.appendChild(option);
            }
        }

        // Initialize passions
        this.initializePassions();

        // Load current profile data
        this.loadProfileData();
    }

    initializePassions() {
        const passions = [
            'Travel', 'Music', 'Movies', 'Sports', 'Reading', 'Cooking', 'Photography',
            'Art', 'Dancing', 'Hiking', 'Gaming', 'Fitness', 'Yoga', 'Coffee',
            'Wine', 'Fashion', 'Technology', 'Animals', 'Nature', 'Beach',
            'Mountains', 'Food', 'Adventure', 'Learning', 'Writing'
        ];

        const container = document.getElementById('passions-container');
        container.innerHTML = passions.map(passion => `
            <div class="passion-chip" onclick="togglePassion('${passion}')" data-passion="${passion}">
                ${passion}
            </div>
        `).join('');
    }

    async loadProfileData() {
        if (!this.userProfile) return;

        // Load age
        const ageSelect = document.getElementById('profile-age');
        if (this.userProfile.age) {
            ageSelect.value = this.userProfile.age;
        }

        // Load description
        const descriptionInput = document.getElementById('profile-description');
        if (this.userProfile.description) {
            descriptionInput.value = this.userProfile.description;
        }

        // Load images
        if (this.userProfile.images) {
            this.userProfile.images.forEach((image, index) => {
                if (image && index < 4) {
                    const placeholder = document.getElementById(`photo-placeholder-${index}`);
                    const imageUri = image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}`;
                    placeholder.innerHTML = `<img src="${imageUri}" class="photo-preview" alt="Profile photo ${index + 1}">`;
                }
            });
        }

        // Load passions
        if (this.userProfile.passions) {
            this.userProfile.passions.forEach(passion => {
                const chip = document.querySelector(`[data-passion="${passion}"]`);
                if (chip) {
                    chip.classList.add('selected');
                }
            });
            this.updatePassionCount();
        }

        // Load verification status
        this.loadVerificationStatus();
    }

    async loadVerificationStatus() {
        try {
            const response = await fetch(`${this.serverAddress}/api/verification/status`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const verificationStatus = await response.json();
                this.updateVerificationUI(verificationStatus);
            }
        } catch (error) {
            console.error('Error loading verification status:', error);
        }
    }

    updateVerificationUI(verificationStatus) {
        const verificationContent = document.getElementById('verification-content');
        if (!verificationContent) return;

        if (verificationStatus.isVerified) {
            // User is verified
            const badgeIcon = verificationStatus.badgeType === 'gold' ? '🥇' : '🥈';
            verificationContent.innerHTML = `
                <div class="verification-status verified">
                    <div class="verification-icon">${badgeIcon}</div>
                    <div class="verification-text">
                        <div class="verification-title">Verified Account</div>
                        <div class="verification-description">Your account is verified with ${verificationStatus.badgeType} badge</div>
                    </div>
                </div>
            `;
        } else if (verificationStatus.request) {
            // User has a pending or rejected request
            if (verificationStatus.request.status === 'pending') {
                verificationContent.innerHTML = `
                    <div class="verification-status pending">
                        <div class="verification-icon">⏳</div>
                        <div class="verification-text">
                            <div class="verification-title">Verification Pending</div>
                            <div class="verification-description">Your request is being reviewed (24-48 hours)</div>
                        </div>
                    </div>
                `;
            } else if (verificationStatus.request.status === 'rejected') {
                const fingerEmojis = { 1: '☝️', 2: '✌️', 3: '🤟', 4: '🖖', 5: '🖐️' };
                const requiredEmoji = fingerEmojis[verificationStatus.request.requiredFingers] || '☝️';

                verificationContent.innerHTML = `
                    <div class="verification-status rejected">
                        <div class="verification-text">
                            <div class="verification-title">Verification Needed</div>
                            <div class="verification-description">${verificationStatus.request.reviewNotes || 'Let\'s try again with a new photo'}</div>
                            ${verificationStatus.request.requiredFingers ? `
                                <div class="rejection-finger-display">
                                    <span class="rejection-finger-emoji">${requiredEmoji}</span>
                                    <span class="rejection-finger-text">${verificationStatus.request.requiredFingers} finger${verificationStatus.request.requiredFingers > 1 ? 's' : ''}</span>
                                </div>
                            ` : ''}
                        </div>
                        <button class="verification-button" onclick="app.startVerification()">
                            <svg><use href="#icon-camera"/></svg>
                            Try Again
                        </button>
                    </div>
                `;
            }
        } else {
            // User hasn't started verification
            verificationContent.innerHTML = `
                <div class="verification-status not-verified">
                    <div class="verification-icon">📸</div>
                    <div class="verification-text">
                        <div class="verification-title">Get Verified</div>
                        <div class="verification-description">Verify your account to get a badge and build trust</div>
                    </div>
                    <button class="verification-button" onclick="app.startVerification()">
                        <svg><use href="#icon-camera"/></svg>
                        Get Verified
                    </button>
                </div>
            `;
        }
    }

    startVerification() {
        // Generate random finger count
        this.requiredFingers = Math.floor(Math.random() * 5) + 1;

        // Update modal content
        const fingerEmojis = { 1: '☝️', 2: '✌️', 3: '🤟', 4: '🖖', 5: '🖐️' };
        const handGesture = document.getElementById('verification-hand-gesture');
        const fingerCount = document.getElementById('verification-finger-count');

        if (handGesture) {
            handGesture.textContent = fingerEmojis[this.requiredFingers] || '☝️';
        }
        if (fingerCount) {
            fingerCount.textContent = `${this.requiredFingers} finger${this.requiredFingers > 1 ? 's' : ''}`;
        }

        // Show instruction modal
        const modal = document.getElementById('verification-instruction-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    async takeVerificationPhoto() {
        // Close instruction modal
        this.closeVerificationInstructionModal();

        // Create file input for camera
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.capture = 'environment'; // Use rear camera if available

        input.onchange = (event) => {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.verificationPhoto = e.target.result;
                    this.showVerificationPhotoModal();
                };
                reader.readAsDataURL(file);
            }
        };

        input.click();
    }

    showVerificationPhotoModal() {
        const modal = document.getElementById('verification-photo-modal');
        const preview = document.getElementById('verification-photo-preview');

        if (modal && preview && this.verificationPhoto) {
            preview.innerHTML = `<img src="${this.verificationPhoto}" alt="Verification photo" style="max-width: 100%; max-height: 300px; border-radius: 12px;">`;
            modal.style.display = 'flex';
        }
    }

    async submitVerificationPhoto() {
        if (!this.verificationPhoto || !this.requiredFingers) {
            alert('Please take a verification photo first.');
            return;
        }

        const submitBtn = document.getElementById('submit-verification-btn');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Submitting...';
        }

        try {
            const response = await fetch(`${this.serverAddress}/api/verification/submit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({
                    photo: this.verificationPhoto,
                    requiredFingers: this.requiredFingers
                })
            });

            if (response.ok) {
                alert('Verification request submitted successfully! We will review it within 24-48 hours.');
                this.closeVerificationPhotoModal();
                this.verificationPhoto = null;
                this.requiredFingers = null;
                // Reload verification status
                this.loadVerificationStatus();
            } else {
                const error = await response.json();
                alert(`Error: ${error.message || 'Failed to submit verification request'}`);
            }
        } catch (error) {
            console.error('Error submitting verification:', error);
            alert('Failed to submit verification request. Please try again.');
        } finally {
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Submit';
            }
        }
    }

    retakeVerificationPhoto() {
        this.closeVerificationPhotoModal();
        this.takeVerificationPhoto();
    }

    closeVerificationInstructionModal() {
        const modal = document.getElementById('verification-instruction-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    closeVerificationPhotoModal() {
        const modal = document.getElementById('verification-photo-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    initializeSettingsView() {
        // Initialize settings with current values
        this.loadSettingsData();
    }

    async loadSettingsData() {
        // Load current settings from localStorage or defaults
        const maxDistance = localStorage.getItem('maxDistance') || '25';
        const minAge = localStorage.getItem('minAge') || '18';
        const maxAge = localStorage.getItem('maxAge') || '100';
        const notificationsEnabled = localStorage.getItem('notificationsEnabled') !== 'false';
        const userGender = localStorage.getItem('userGender') || '';
        const interestedIn = localStorage.getItem('interestedIn') || 'both';
        const lookingFor = localStorage.getItem('lookingFor') || 'relationship';

        // Update UI
        const distanceSlider = document.getElementById('distance-slider');
        const distanceValue = document.getElementById('distance-value');
        const minAgeSlider = document.getElementById('min-age-slider');
        const minAgeValue = document.getElementById('min-age-value');
        const maxAgeSlider = document.getElementById('max-age-slider');
        const maxAgeValue = document.getElementById('max-age-value');

        if (distanceSlider && distanceValue) {
            distanceSlider.value = maxDistance;
            distanceValue.textContent = `${maxDistance} km`;
        }

        if (minAgeSlider && minAgeValue) {
            minAgeSlider.value = minAge;
            minAgeValue.textContent = `${minAge} years`;
        }

        if (maxAgeSlider && maxAgeValue) {
            maxAgeSlider.value = maxAge;
            maxAgeValue.textContent = `${maxAge} years`;
        }

        // Update gender and preference UI
        updateMainGenderUI(userGender);
        updateMainInterestedInUI(interestedIn);
        updateMainLookingForUI(lookingFor);

        // Update instruction text
        updateInstructionText(lookingFor);

        document.getElementById('notifications-toggle').checked = notificationsEnabled;

        // Load premium status
        await this.loadPremiumStatus();

        // Check location permission
        this.checkLocationPermissionStatus();
    }

    async loadPremiumStatus() {
        if (!this.user) return;

        const refreshBtn = document.getElementById('premium-refresh-btn');
        const premiumContent = document.getElementById('premium-content');

        try {
            if (refreshBtn) {
                refreshBtn.disabled = true;
                refreshBtn.style.opacity = '0.5';
            }

            const response = await fetch(`${this.serverAddress}/api/premium/status`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.premiumStatus = data;
                this.renderPremiumSection(data);
            } else {
                // Show authentication required message
                this.renderPremiumAuthRequired();
            }
        } catch (error) {
            console.error('Error loading premium status:', error);
            this.renderPremiumAuthRequired();
        } finally {
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.style.opacity = '1';
            }
        }
    }

    renderPremiumSection(premiumStatus) {
        const premiumContent = document.getElementById('premium-content');
        const premiumBadge = document.getElementById('premium-badge');

        if (!premiumContent) return;

        if (premiumStatus && premiumStatus.isPremium) {
            // Show premium badge
            if (premiumBadge) {
                premiumBadge.style.display = 'flex';
            }

            // Render premium features
            premiumContent.innerHTML = `
                <div class="premium-info">
                    <div class="premium-status">
                        <div class="premium-badge-large">
                            <svg class="premium-icon"><use href="#icon-star"/></svg>
                            PREMIUM ACTIVE
                        </div>
                        ${premiumStatus.daysRemaining > 0 ?
                            `<div class="premium-days">${premiumStatus.daysRemaining} days remaining</div>` :
                            `<div class="premium-unlimited">Unlimited</div>`
                        }
                    </div>
                </div>

                <div class="premium-feature">
                    <div class="feature-header">
                        <svg class="feature-icon"><use href="#icon-location"/></svg>
                        <h4>Custom Location</h4>
                    </div>
                    ${premiumStatus.customLocation && premiumStatus.customLocation.enabled ? `
                        <div class="custom-location-active">
                            <p class="location-text premium">
                                Active: ${premiumStatus.customLocation.city || 'Unknown'}, ${premiumStatus.customLocation.country || 'Unknown'}
                            </p>
                            <div class="location-buttons">
                                <button class="change-location-btn" onclick="changeCustomLocation()">
                                    <svg><use href="#icon-location"/></svg>
                                    Change Location
                                </button>
                                <button class="disable-location-btn" onclick="disableCustomLocation()">
                                    Disable
                                </button>
                            </div>
                        </div>
                    ` : `
                        <div class="custom-location-inactive">
                            <p class="location-text">Set a custom location to match with people anywhere in the world</p>
                            <button class="enable-location-btn" onclick="enableCustomLocation()">
                                <svg><use href="#icon-location"/></svg>
                                Set Custom Location
                            </button>
                        </div>
                    `}
                </div>
            `;
        } else {
            // Hide premium badge
            if (premiumBadge) {
                premiumBadge.style.display = 'none';
            }

            // Show upgrade options
            premiumContent.innerHTML = `
                <div class="premium-upgrade">
                    <div class="upgrade-info">
                        <h4>Unlock Premium Features</h4>
                        <ul class="premium-features-list">
                            <li>
                                <svg><use href="#icon-location"/></svg>
                                Change your location to match anywhere
                            </li>
                            <li>
                                <svg><use href="#icon-star"/></svg>
                                Priority matching
                            </li>
                            <li>
                                <svg><use href="#icon-heart"/></svg>
                                Unlimited likes
                            </li>
                        </ul>
                    </div>
                    <button class="upgrade-btn" onclick="upgradeToPremium()">
                        <svg><use href="#icon-star"/></svg>
                        Upgrade to Premium
                    </button>
                </div>
            `;
        }
    }

    renderPremiumAuthRequired() {
        const premiumContent = document.getElementById('premium-content');
        const premiumBadge = document.getElementById('premium-badge');

        if (premiumBadge) {
            premiumBadge.style.display = 'none';
        }

        if (premiumContent) {
            premiumContent.innerHTML = `
                <div class="auth-warning">
                    <svg class="warning-icon"><use href="#icon-close"/></svg>
                    <h4>Authentication Required</h4>
                    <p>You need to log in again to check your premium status and access premium features.</p>
                    <button class="login-btn" onclick="logout()">
                        <svg><use href="#icon-user"/></svg>
                        Log In Again
                    </button>
                </div>
            `;
        }
    }

    async checkLocationPermissionStatus() {
        const statusElement = document.getElementById('location-status');
        const buttonElement = document.getElementById('location-btn');

        if (!navigator.geolocation) {
            statusElement.textContent = 'Not supported';
            buttonElement.style.display = 'none';
            return;
        }

        try {
            const position = await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, { timeout: 5000 });
            });
            statusElement.textContent = 'Enabled';
            buttonElement.textContent = 'Enabled';
            buttonElement.disabled = true;
        } catch (error) {
            statusElement.textContent = 'Disabled';
            buttonElement.textContent = 'Enable';
            buttonElement.disabled = false;
        }
    }

    logout() {
        localStorage.removeItem('authToken');
        if (this.socket) {
            this.socket.disconnect();
        }
        this.user = null;
        this.userProfile = null;
        this.matches = [];
        this.currentChat = null;
        this.premiumStatus = null;
        this.showLoginScreen();
    }

    // Premium functionality
    async loadPremiumStatus() {
        try {
            const token = localStorage.getItem('authToken');
            if (!token) return;

            const response = await fetch(`${this.serverAddress}/api/premium/status`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                this.premiumStatus = await response.json();
                this.updatePremiumUI();
            }
        } catch (error) {
            console.error('Error loading premium status:', error);
        }
    }

    updatePremiumUI() {
        if (!this.premiumStatus) return;

        // Update settings view with premium features
        const settingsView = document.getElementById('settings-view');
        if (settingsView && this.premiumStatus.isPremium) {
            this.addPremiumSettingsSection();
        }

        // Add premium badge to user info if premium
        if (this.premiumStatus.isPremium) {
            this.addPremiumBadge();
        }

        // Update location indicator
        this.updateLocationIndicator();
    }

    updateLocationIndicator() {
        // Add location indicator to the header
        const header = document.querySelector('.app-header');
        if (!header) return;

        // Remove existing location indicator
        const existingIndicator = header.querySelector('.location-indicator');
        if (existingIndicator) existingIndicator.remove();

        // Create new location indicator
        const locationIndicator = document.createElement('div');
        locationIndicator.className = 'location-indicator';

        if (this.premiumStatus && this.premiumStatus.customLocation && this.premiumStatus.customLocation.enabled) {
            locationIndicator.innerHTML = `
                <svg class="location-icon premium"><use href="#icon-location"/></svg>
                <span class="location-text premium">${this.premiumStatus.customLocation.city}, ${this.premiumStatus.customLocation.country}</span>
                <div class="premium-badge-small">
                    <svg class="premium-icon"><use href="#icon-star"/></svg>
                    <span>PREMIUM</span>
                </div>
            `;
        } else {
            locationIndicator.innerHTML = `
                <svg class="location-icon"><use href="#icon-location"/></svg>
                <span class="location-text">Your Location</span>
            `;
        }

        // Insert location indicator in header
        const userInfo = header.querySelector('.user-info');
        if (userInfo) {
            userInfo.appendChild(locationIndicator);
        }
    }

    addPremiumBadge() {
        const userInfo = document.querySelector('.user-info');
        if (userInfo && !userInfo.querySelector('.premium-badge')) {
            const badge = document.createElement('div');
            badge.className = 'premium-badge';
            badge.innerHTML = `
                <svg class="premium-icon"><use href="#icon-star"/></svg>
                <span>PREMIUM</span>
            `;
            userInfo.appendChild(badge);
        }
    }

    addPremiumSettingsSection() {
        const settingsContent = document.querySelector('#settings-view .settings-content');
        if (!settingsContent || settingsContent.querySelector('.premium-section')) return;

        const premiumSection = document.createElement('div');
        premiumSection.className = 'settings-section premium-section';
        premiumSection.innerHTML = `
            <h3 class="section-title">
                <svg class="section-icon"><use href="#icon-star"/></svg>
                Premium Features
            </h3>
            <div class="premium-info">
                <div class="premium-status">
                    <span class="premium-badge-large">
                        <svg class="premium-icon"><use href="#icon-star"/></svg>
                        PREMIUM ACTIVE
                    </span>
                    ${this.premiumStatus.daysRemaining > 0 ?
                        `<span class="premium-days">${this.premiumStatus.daysRemaining} days remaining</span>` :
                        '<span class="premium-unlimited">Unlimited</span>'
                    }
                </div>
            </div>
            <div class="premium-feature">
                <div class="feature-header">
                    <svg class="feature-icon"><use href="#icon-location"/></svg>
                    <span class="feature-title">Custom Location</span>
                </div>
                <div class="feature-content">
                    ${this.premiumStatus.customLocation.enabled ?
                        `<div class="custom-location-active">
                            <p class="location-text">Active: ${this.premiumStatus.customLocation.city}, ${this.premiumStatus.customLocation.country}</p>
                            <div class="location-buttons">
                                <button class="btn btn-secondary" onclick="showLocationModal()">Change Location</button>
                                <button class="btn btn-danger" onclick="disableCustomLocation()">Disable</button>
                            </div>
                        </div>` :
                        `<div class="custom-location-inactive">
                            <p class="feature-description">Change your location to appear in different cities and match with users worldwide.</p>
                            <div class="location-setup-buttons">
                                <button class="btn btn-primary" onclick="showMapLocationPicker()">
                                    <svg class="btn-icon"><use href="#icon-location"/></svg>
                                    Pick on Map
                                </button>
                                <button class="btn btn-secondary" onclick="showLocationModal()">
                                    <svg class="btn-icon"><use href="#icon-pencil"/></svg>
                                    Enter Manually
                                </button>
                            </div>
                        </div>`
                    }
                </div>
            </div>
        `;

        settingsContent.appendChild(premiumSection);
    }

    async updateCustomLocation(city, country) {
        try {
            const token = localStorage.getItem('authToken');
            if (!token) return;

            // Mock coordinates for demo
            const mockCoordinates = {
                latitude: 40.7128 + (Math.random() - 0.5) * 10,
                longitude: -74.0060 + (Math.random() - 0.5) * 10
            };

            const response = await fetch(`${this.serverAddress}/api/premium/location`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    city: city.trim(),
                    country: country.trim(),
                    latitude: mockCoordinates.latitude,
                    longitude: mockCoordinates.longitude
                }),
            });

            if (response.ok) {
                this.showNotification('Custom location updated successfully!');
                await this.loadPremiumStatus(); // Refresh status
                this.updatePremiumUI();
                return true;
            } else {
                const error = await response.json();
                this.showAlert(error.error || 'Failed to update location');
                return false;
            }
        } catch (error) {
            console.error('Error updating custom location:', error);
            this.showAlert('Failed to update location');
            return false;
        }
    }

    async disableCustomLocation() {
        try {
            const token = localStorage.getItem('authToken');
            if (!token) return;

            const response = await fetch(`${this.serverAddress}/api/premium/location`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                this.showNotification('Custom location disabled. Using your real location now.');
                await this.loadPremiumStatus(); // Refresh status
                this.updatePremiumUI();
            } else {
                const error = await response.json();
                this.showAlert(error.error || 'Failed to disable custom location');
            }
        } catch (error) {
            console.error('Error disabling custom location:', error);
            this.showAlert('Failed to disable custom location');
        }
    }
}

// Initialize the app
const app = new ShakeMatchApp();

// Global functions for HTML onclick handlers
function handleLogin(event) {
    app.handleLogin(event);
}

function handleSocialLogin(provider) {
    app.handleSocialLogin(provider);
}

function showScreen(screenName) {
    if (screenName === 'profile') {
        app.showView(screenName);
    } else if (screenName === 'settings') {
        // For settings, toggle the dropdown instead of showing full screen
        toggleSettingsDropdown();
    }
}

function showView(viewName) {
    app.showView(viewName);
}

function handleShake() {
    app.handleShake();
}

function handleChatKeyPress(event) {
    const textarea = event.target;

    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    } else {
        // Auto-resize textarea
        setTimeout(() => {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
        }, 0);
    }
}

function sendMessage() {
    const input = document.getElementById('chat-input');
    const sendBtn = document.getElementById('send-btn');
    const message = input.value.trim();

    if (!message || !app.currentChat || !app.user) return;

    // Disable send button temporarily
    sendBtn.disabled = true;

    console.log('=== SENDING MESSAGE ===');
    console.log('Message text:', message);
    console.log('Current user ID:', app.user.id);
    console.log('Target user ID:', app.currentChat.userId);

    // Ensure consistent user ID formats
    const senderId = app.user.id.toString();
    const receiverId = app.currentChat.userId.toString();

    // Create message data matching server expectations
    const messageData = {
        id: Date.now().toString(), // Simple ID generation
        senderId: senderId,
        senderUsername: app.user.username,
        receiverId: receiverId,
        receiverUsername: app.currentChat.username,
        text: message,
        timestamp: new Date().toISOString()
    };

    console.log('Message data to send:', JSON.stringify(messageData, null, 2));

    // Send message via socket with correct event name
    app.socket.emit('sendMessage', messageData);

    // Add message to chat immediately
    app.addMessageToChat({
        senderId: senderId,
        text: message,
        timestamp: messageData.timestamp
    });

    input.value = '';
    input.style.height = 'auto'; // Reset textarea height

    // Re-enable send button
    setTimeout(() => {
        sendBtn.disabled = false;
    }, 500);
}

function viewProfile() {
    if (app.currentChat) {
        app.viewProfile(app.currentChat.userId);
    }
}

// Profile functions
function selectPhoto(index) {
    document.getElementById(`photo-${index}`).click();
}

function handlePhotoSelect(index, event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        const placeholder = document.getElementById(`photo-placeholder-${index}`);
        placeholder.innerHTML = `<img src="${e.target.result}" class="photo-preview" alt="Profile photo ${index + 1}">`;
    };
    reader.readAsDataURL(file);
}

function togglePassion(passion) {
    const chip = document.querySelector(`[data-passion="${passion}"]`);
    const isSelected = chip.classList.contains('selected');
    const selectedCount = document.querySelectorAll('.passion-chip.selected').length;

    if (isSelected) {
        chip.classList.remove('selected');
    } else if (selectedCount < 6) {
        chip.classList.add('selected');
    } else {
        app.showAlert('You can select maximum 6 passions');
        return;
    }

    updatePassionCount();
}

function updatePassionCount() {
    const selectedCount = document.querySelectorAll('.passion-chip.selected').length;
    const countElement = document.getElementById('passion-count');
    const statusElement = document.querySelector('.passion-status');

    countElement.textContent = `${selectedCount}/6 passions selected`;

    if (selectedCount < 3) {
        countElement.textContent += ` (need ${3 - selectedCount} more)`;
        statusElement.className = 'passion-status warning';
    } else {
        statusElement.className = 'passion-status success';
    }
}

async function saveProfile() {
    const saveBtn = document.getElementById('profile-save-btn');
    saveBtn.disabled = true;
    saveBtn.textContent = 'Saving...';

    try {
        // Collect form data
        const age = document.getElementById('profile-age').value;
        const description = document.getElementById('profile-description').value;
        const selectedPassions = Array.from(document.querySelectorAll('.passion-chip.selected'))
            .map(chip => chip.dataset.passion);

        // Collect images
        const images = [];
        for (let i = 0; i < 4; i++) {
            const img = document.querySelector(`#photo-placeholder-${i} img`);
            if (img) {
                images.push(img.src);
            }
        }

        // Validate required fields
        if (!description.trim()) {
            app.showAlert('Description is required');
            return;
        }

        if (!age) {
            app.showAlert('Age is required');
            return;
        }

        if (selectedPassions.length < 3) {
            app.showAlert('Please select at least 3 passions');
            return;
        }

        if (images.length === 0) {
            app.showAlert('Please add at least one photo');
            return;
        }

        // Save profile
        const response = await fetch(`${app.serverAddress}/api/profile`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify({
                age: parseInt(age),
                description: description.trim(),
                passions: selectedPassions,
                images: images
            })
        });

        if (response.ok) {
            const data = await response.json();
            app.userProfile = data.profile;
            app.updateProfileIcon();
            app.showNotification('Profile updated successfully!');
        } else {
            const error = await response.json();
            app.showAlert(error.error || 'Failed to save profile');
        }
    } catch (error) {
        console.error('Profile save error:', error);
        app.showAlert('Failed to save profile. Please try again.');
    } finally {
        saveBtn.disabled = false;
        saveBtn.textContent = 'Save';
    }
}

// Settings dropdown functions
function toggleSettingsDropdown() {
    const dropdown = document.getElementById('settings-dropdown');
    dropdown.classList.toggle('active');

    // Initialize dropdown settings when opened
    if (dropdown.classList.contains('active')) {
        initializeDropdownSettings();
    }
}

function initializeDropdownSettings() {
    // Load current settings from localStorage or defaults
    const maxDistance = localStorage.getItem('maxDistance') || '25';
    const minAge = localStorage.getItem('minAge') || '18';
    const maxAge = localStorage.getItem('maxAge') || '100';
    const userGender = localStorage.getItem('userGender') || '';
    const interestedIn = localStorage.getItem('interestedIn') || 'both';
    const lookingFor = localStorage.getItem('lookingFor') || 'relationship';

    // Update dropdown UI
    const distanceSlider = document.getElementById('dropdown-distance-slider');
    const distanceValue = document.getElementById('dropdown-distance-value');
    const minAgeSlider = document.getElementById('dropdown-min-age-slider');
    const maxAgeSlider = document.getElementById('dropdown-max-age-slider');
    const ageValue = document.getElementById('dropdown-age-value');

    if (distanceSlider && distanceValue) {
        distanceSlider.value = maxDistance;
        distanceValue.textContent = `${maxDistance} km`;
    }

    if (minAgeSlider && maxAgeSlider && ageValue) {
        minAgeSlider.value = minAge;
        maxAgeSlider.value = maxAge;
        ageValue.textContent = `${minAge}-${maxAge} years`;
    }

    // Update gender and preference UI
    updateDropdownGenderUI(userGender);
    updateDropdownInterestedInUI(interestedIn);
    updateDropdownLookingForUI(lookingFor);

    // Update instruction text on page load
    updateInstructionText(lookingFor);

    // Load premium status for dropdown
    if (app && app.loadPremiumStatus) {
        app.loadPremiumStatus().then(() => {
            updateDropdownPremiumUI();
        });
    }
}

function updateDropdownPremiumUI() {
    const premiumSection = document.getElementById('premium-dropdown-section');
    const premiumBadge = document.getElementById('premium-dropdown-badge');
    const premiumContent = document.getElementById('premium-dropdown-content');

    if (!app || !app.premiumStatus) return;

    if (app.premiumStatus.isPremium) {
        premiumBadge.style.display = 'flex';
        premiumContent.innerHTML = `
            <div class="premium-info-compact">
                <p style="color: #b8860b; font-weight: 600; margin: 0 0 8px 0;">Premium Active</p>
                ${app.premiumStatus.daysRemaining ? `<p style="color: #666; font-size: 12px; margin: 0;">${app.premiumStatus.daysRemaining} days remaining</p>` : ''}
            </div>
        `;
    } else {
        premiumBadge.style.display = 'none';
        premiumContent.innerHTML = `
            <div class="premium-upgrade-compact">
                <p style="color: #666; font-size: 12px; margin: 0 0 8px 0;">Upgrade to access premium features</p>
                <button class="btn btn-primary btn-sm" onclick="showView('settings')" style="font-size: 12px; padding: 6px 12px;">
                    View Premium
                </button>
            </div>
        `;
    }
}

// Settings functions
function updateDistance(value) {
    document.getElementById('distance-value').textContent = `${value} km`;
    localStorage.setItem('maxDistance', value);
}

function updateDropdownDistance(value) {
    document.getElementById('dropdown-distance-value').textContent = `${value} km`;
    localStorage.setItem('maxDistance', value);

    // Also update main settings if they exist
    const mainSlider = document.getElementById('distance-slider');
    const mainValue = document.getElementById('distance-value');
    if (mainSlider && mainValue) {
        mainSlider.value = value;
        mainValue.textContent = `${value} km`;
    }
}

function updateMinAge(value) {
    document.getElementById('min-age-value').textContent = `${value} years`;
    localStorage.setItem('minAge', value);

    // Ensure max age is not less than min age
    const maxAgeSlider = document.getElementById('max-age-slider');
    if (parseInt(maxAgeSlider.value) < parseInt(value)) {
        maxAgeSlider.value = value;
        updateMaxAge(value);
    }
}

function updateDropdownMinAge(value) {
    localStorage.setItem('minAge', value);

    // Ensure max age is not less than min age
    const maxAgeSlider = document.getElementById('dropdown-max-age-slider');
    const maxAge = parseInt(maxAgeSlider.value);
    const minAge = parseInt(value);

    if (maxAge < minAge) {
        maxAgeSlider.value = value;
        localStorage.setItem('maxAge', value);
    }

    // Update age range display
    const ageValue = document.getElementById('dropdown-age-value');
    if (ageValue) {
        ageValue.textContent = `${value}-${maxAgeSlider.value} years`;
    }

    // Also update main settings if they exist
    const mainMinSlider = document.getElementById('min-age-slider');
    const mainMinValue = document.getElementById('min-age-value');
    if (mainMinSlider && mainMinValue) {
        mainMinSlider.value = value;
        mainMinValue.textContent = `${value} years`;
    }
}

function updateDropdownMaxAge(value) {
    localStorage.setItem('maxAge', value);

    // Ensure min age is not greater than max age
    const minAgeSlider = document.getElementById('dropdown-min-age-slider');
    const minAge = parseInt(minAgeSlider.value);
    const maxAge = parseInt(value);

    if (minAge > maxAge) {
        minAgeSlider.value = value;
        localStorage.setItem('minAge', value);
    }

    // Update age range display
    const ageValue = document.getElementById('dropdown-age-value');
    if (ageValue) {
        ageValue.textContent = `${minAgeSlider.value}-${value} years`;
    }

    // Also update main settings if they exist
    const mainMaxSlider = document.getElementById('max-age-slider');
    const mainMaxValue = document.getElementById('max-age-value');
    if (mainMaxSlider && mainMaxValue) {
        mainMaxSlider.value = value;
        mainMaxValue.textContent = `${value} years`;
    }
}

function updateMaxAge(value) {
    document.getElementById('max-age-value').textContent = `${value} years`;
    localStorage.setItem('maxAge', value);

    // Ensure min age is not greater than max age
    const minAgeSlider = document.getElementById('min-age-slider');
    if (parseInt(minAgeSlider.value) > parseInt(value)) {
        minAgeSlider.value = value;
        updateMinAge(value);
    }
}

function toggleNotifications(enabled) {
    localStorage.setItem('notificationsEnabled', enabled);
    if (enabled) {
        app.showNotification('Notifications enabled');
    } else {
        app.showNotification('Notifications disabled');
    }
}

async function requestLocationPermission() {
    try {
        const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject);
        });
        app.checkLocationPermissionStatus();
        app.showNotification('Location permission granted');
    } catch (error) {
        app.showAlert('Location permission denied. Please enable it in your browser settings.');
    }
}

// Chat functions
function showChatOptions() {
    if (!app.currentChat) return;

    const options = [
        'View Profile',
        'Block User',
        'Delete Chat'
    ];

    // Simple implementation - you could create a proper modal
    const choice = prompt('Choose an option:\n1. View Profile\n2. Block User\n3. Delete Chat\n\nEnter number (1-3):');

    switch (choice) {
        case '1':
            viewProfile();
            break;
        case '2':
            if (confirm(`Are you sure you want to block ${app.currentChat.username}?`)) {
                // Implement block functionality
                app.showAlert('Block functionality will be implemented');
            }
            break;
        case '3':
            if (confirm(`Are you sure you want to delete this chat with ${app.currentChat.username}?`)) {
                // Implement delete chat functionality
                app.showAlert('Delete chat functionality will be implemented');
            }
            break;
    }
}

// Profile Modal Functions
function closeProfileModal() {
    const modal = document.getElementById('profile-modal');
    modal.classList.remove('active');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

function nextProfilePhoto() {
    if (!app.currentProfileImages || app.currentProfileImages.length <= 1) return;
    const nextIndex = (app.currentImageIndex + 1) % app.currentProfileImages.length;
    app.displayProfileImage(nextIndex);
}

function prevProfilePhoto() {
    if (!app.currentProfileImages || app.currentProfileImages.length <= 1) return;
    const prevIndex = app.currentImageIndex === 0 ? app.currentProfileImages.length - 1 : app.currentImageIndex - 1;
    app.displayProfileImage(prevIndex);
}

function showProfileOptions() {
    if (!app.currentProfileUser) return;

    // Simple implementation for now
    const options = [
        'Block User',
        'Report User'
    ];

    const choice = confirm(`Block ${app.currentProfileUser.username}?`);
    if (choice) {
        app.showAlert('Block functionality will be implemented');
    }
}

// Chat Options Modal Functions
function showChatOptions() {
    if (!app.currentChat) return;

    const modal = document.getElementById('chat-options-modal');
    modal.style.display = 'flex';
    setTimeout(() => modal.classList.add('active'), 10);
}

function closeChatOptionsModal() {
    const modal = document.getElementById('chat-options-modal');
    modal.classList.remove('active');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

function viewProfileFromChat() {
    closeChatOptionsModal();
    if (app.currentChat) {
        app.viewProfile(app.currentChat.userId);
    }
}

function blockUserFromChat() {
    closeChatOptionsModal();
    if (app.currentChat) {
        if (confirm(`Are you sure you want to block ${app.currentChat.username}?`)) {
            app.showAlert('Block functionality will be implemented');
        }
    }
}

function deleteChatFromChat() {
    closeChatOptionsModal();
    if (app.currentChat) {
        if (confirm(`Are you sure you want to delete this chat with ${app.currentChat.username}?`)) {
            app.showAlert('Delete chat functionality will be implemented');
        }
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    const profileModal = document.getElementById('profile-modal');
    const chatOptionsModal = document.getElementById('chat-options-modal');

    if (event.target === profileModal) {
        closeProfileModal();
    }

    if (event.target === chatOptionsModal) {
        closeChatOptionsModal();
    }
});

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        app.logout();
    }
}

// Premium functions
function showLocationModal() {
    const modal = document.createElement('div');
    modal.className = 'modal location-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Set Custom Location</h3>
                <button class="close-btn" onclick="closeLocationModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p class="modal-description">
                    Choose a city where you'd like to appear for matching. This is a premium feature that lets you connect with users worldwide.
                </p>
                <div class="input-group">
                    <label for="location-city">City</label>
                    <input type="text" id="location-city" placeholder="Enter city name" />
                </div>
                <div class="input-group">
                    <label for="location-country">Country</label>
                    <input type="text" id="location-country" placeholder="Enter country name" />
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeLocationModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveCustomLocation()">Save Location</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    setTimeout(() => modal.classList.add('active'), 10);
}

function closeLocationModal() {
    const modal = document.querySelector('.location-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => modal.remove(), 300);
    }
}

async function saveCustomLocation() {
    const city = document.getElementById('location-city').value.trim();
    const country = document.getElementById('location-country').value.trim();

    if (!city || !country) {
        app.showAlert('Please enter both city and country');
        return;
    }

    const saveBtn = document.querySelector('.location-modal .btn-primary');
    saveBtn.disabled = true;
    saveBtn.textContent = 'Saving...';

    const success = await app.updateCustomLocation(city, country);
    if (success) {
        closeLocationModal();
    }

    saveBtn.disabled = false;
    saveBtn.textContent = 'Save Location';
}

async function disableCustomLocation() {
    if (confirm('Are you sure you want to disable custom location? You will appear in your real location.')) {
        await app.disableCustomLocation();
    }
}

// Map-based location picker
function showMapLocationPicker() {
    const modal = document.createElement('div');
    modal.className = 'modal map-location-modal';
    modal.innerHTML = `
        <div class="modal-content map-modal-content">
            <div class="modal-header">
                <h3>Choose Location on Map</h3>
                <button class="close-btn" onclick="closeMapLocationModal()">&times;</button>
            </div>
            <div class="map-container">
                <div id="location-map" class="location-map"></div>
                <div class="map-controls">
                    <button class="btn btn-primary" id="reset-location-btn" onclick="resetToRealLocation()">
                        📍 Use My Real Location
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeMapLocationModal()">Cancel</button>
                <button class="btn btn-primary" id="confirm-map-location" onclick="confirmMapLocation()" disabled>
                    Confirm Location
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    setTimeout(() => {
        modal.classList.add('active');
        initializeMap();
    }, 10);
}

function closeMapLocationModal() {
    const modal = document.querySelector('.map-location-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => modal.remove(), 300);
    }
}

let selectedMapLocation = null;
let map = null;
let marker = null;

function initializeMap() {
    const mapContainer = document.getElementById('location-map');

    // Add Leaflet CSS and JS if not already loaded
    if (!window.L) {
        const leafletCSS = document.createElement('link');
        leafletCSS.rel = 'stylesheet';
        leafletCSS.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        document.head.appendChild(leafletCSS);

        const leafletJS = document.createElement('script');
        leafletJS.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        leafletJS.onload = () => initLeafletMap();
        document.head.appendChild(leafletJS);
    } else {
        initLeafletMap();
    }
}

function initLeafletMap() {
    // Initialize the map
    map = L.map('location-map').setView([40.7128, -74.0060], 4);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19
    }).addTo(map);

    // Add satellite imagery option
    const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics',
        maxZoom: 19
    });

    // Layer control
    const baseMaps = {
        "Street Map": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }),
        "Satellite": satelliteLayer
    };

    L.control.layers(baseMaps).addTo(map);

    // Custom marker icon
    const customIcon = L.divIcon({
        html: `
            <div style="
                width: 30px;
                height: 40px;
                position: relative;
                filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
            ">
                <svg width="30" height="40" viewBox="0 0 30 40">
                    <path d="M15 0C6.716 0 0 6.716 0 15c0 15 15 25 15 25s15-10 15-25C30 6.716 23.284 0 15 0z" fill="#FF4444"/>
                    <circle cx="15" cy="15" r="7" fill="white"/>
                    <circle cx="15" cy="15" r="3" fill="#FF4444"/>
                </svg>
            </div>
        `,
        className: 'custom-marker',
        iconSize: [30, 40],
        iconAnchor: [15, 40]
    });

    // Handle map clicks
    map.on('click', function(e) {
        const lat = e.latlng.lat;
        const lng = e.latlng.lng;

        console.log('Map clicked at:', lat, lng);

        // Remove existing marker
        if (marker) {
            map.removeLayer(marker);
        }

        // Add new marker
        marker = L.marker([lat, lng], {icon: customIcon}).addTo(map);

        selectedMapLocation = { latitude: lat, longitude: lng, address: 'Custom Location' };

        // Enable confirm button immediately
        const confirmBtn = document.getElementById('confirm-map-location');
        if (confirmBtn) {
            confirmBtn.disabled = false;
        }

        // Try to get address using Nominatim (optional)
        fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`)
            .then(response => response.json())
            .then(data => {
                if (data && data.display_name) {
                    selectedMapLocation.address = data.display_name;
                    console.log('Updated address:', data.display_name);
                }
            })
            .catch(error => {
                console.log('Reverse geocoding failed:', error);
                // Keep the default 'Custom Location' address
            });
    });
}



// Reset to real location function
function resetToRealLocation() {
    const resetBtn = document.getElementById('reset-location-btn');
    if (!resetBtn) return;

    if (navigator.geolocation) {
        resetBtn.textContent = '📍 Getting location...';
        resetBtn.disabled = true;

        const options = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        };

        navigator.geolocation.getCurrentPosition(function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;

            console.log('Got real location:', lat, lng);

            // Remove existing marker
            if (marker) {
                map.removeLayer(marker);
            }

            // Add marker at real location
            const customIcon = L.divIcon({
                html: `
                    <div style="
                        width: 30px;
                        height: 40px;
                        position: relative;
                        filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
                    ">
                        <svg width="30" height="40" viewBox="0 0 30 40">
                            <path d="M15 0C6.716 0 0 6.716 0 15c0 15 15 25 15 25s15-10 15-25C30 6.716 23.284 0 15 0z" fill="#FF4444"/>
                            <circle cx="15" cy="15" r="7" fill="white"/>
                            <circle cx="15" cy="15" r="3" fill="#FF4444"/>
                        </svg>
                    </div>
                `,
                className: 'custom-marker',
                iconSize: [30, 40],
                iconAnchor: [15, 40]
            });

            marker = L.marker([lat, lng], {icon: customIcon}).addTo(map);

            // Center map on real location
            map.setView([lat, lng], 13);

            selectedMapLocation = { latitude: lat, longitude: lng };

            // Enable confirm button
            const confirmBtn = document.getElementById('confirm-map-location');
            if (confirmBtn) {
                confirmBtn.disabled = false;
            }

            // Get address for real location
            fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.display_name) {
                        selectedMapLocation.address = data.display_name;
                    } else {
                        selectedMapLocation.address = 'Your Real Location';
                    }
                })
                .catch(error => {
                    console.log('Reverse geocoding failed:', error);
                    selectedMapLocation.address = 'Your Real Location';
                });

            resetBtn.textContent = '📍 Use My Real Location';
            resetBtn.disabled = false;
        }, function(error) {
            console.log('Geolocation error:', error.code, error.message);
            let errorMessage = 'Could not get your location. ';

            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage += 'Location access denied. Please enable location permissions in your browser.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage += 'Location information unavailable.';
                    break;
                case error.TIMEOUT:
                    errorMessage += 'Location request timed out.';
                    break;
                default:
                    errorMessage += 'Unknown error occurred.';
                    break;
            }

            if (window.app && app.showAlert) {
                app.showAlert(errorMessage);
            } else {
                alert(errorMessage);
            }
            resetBtn.textContent = '📍 Use My Real Location';
            resetBtn.disabled = false;
        }, options);
    } else {
        const errorMessage = 'Geolocation is not supported by this browser.';
        if (window.app && app.showAlert) {
            app.showAlert(errorMessage);
        } else {
            alert(errorMessage);
        }
        resetBtn.textContent = '📍 Use My Real Location';
        resetBtn.disabled = false;
    }
}

async function confirmMapLocation() {
    if (!selectedMapLocation) {
        app.showAlert('Please select a location on the map');
        return;
    }

    const confirmBtn = document.getElementById('confirm-map-location');
    confirmBtn.disabled = true;
    confirmBtn.textContent = 'Setting Location...';

    // Extract city and country from address or use defaults
    let city = 'Custom Location';
    let country = 'Selected Area';

    if (selectedMapLocation.address) {
        const addressParts = selectedMapLocation.address.split(', ');
        if (addressParts.length >= 2) {
            city = addressParts[0];
            country = addressParts[addressParts.length - 1];
        }
    }

    const success = await app.updateCustomLocation(city, country);
    if (success) {
        closeMapLocationModal();
    }

    confirmBtn.disabled = false;
    confirmBtn.textContent = 'Confirm Location';
}

// Premium functions
function loadPremiumStatus() {
    app.loadPremiumStatus();
}

function upgradeToPremium() {
    app.showAlert('Premium upgrade functionality will be implemented');
}

function enableCustomLocation() {
    app.showAlert('Custom location setup will be implemented');
}

function changeCustomLocation() {
    app.showAlert('Change location functionality will be implemented');
}

function disableCustomLocation() {
    if (confirm('Are you sure you want to disable custom location?')) {
        app.showAlert('Disable location functionality will be implemented');
    }
}

// Gender and preference functions
function updateDropdownGender(gender) {
    localStorage.setItem('userGender', gender);
    updateDropdownGenderUI(gender);

    // Also update main settings if they exist
    updateMainGenderUI(gender);

    // Save to server
    savePreferencesToServer();
}

function updateDropdownGenderUI(gender) {
    // Remove selected class from all gender buttons
    document.querySelectorAll('[id^="dropdown-gender-"]').forEach(btn => {
        btn.classList.remove('selected');
    });

    // Add selected class to chosen gender
    const selectedBtn = document.getElementById(`dropdown-gender-${gender}`);
    if (selectedBtn) {
        selectedBtn.classList.add('selected');
    }
}

function updateDropdownInterestedIn(interested) {
    localStorage.setItem('interestedIn', interested);
    updateDropdownInterestedInUI(interested);

    // Also update main settings if they exist
    updateMainInterestedInUI(interested);

    // Save to server
    savePreferencesToServer();
}

function updateDropdownInterestedInUI(interested) {
    // Remove selected class from all interested buttons
    document.querySelectorAll('[id^="dropdown-interested-"]').forEach(btn => {
        btn.classList.remove('selected');
    });

    // Add selected class to chosen option
    const selectedBtn = document.getElementById(`dropdown-interested-${interested}`);
    if (selectedBtn) {
        selectedBtn.classList.add('selected');
    }
}

function updateDropdownLookingFor(lookingFor) {
    localStorage.setItem('lookingFor', lookingFor);
    updateDropdownLookingForUI(lookingFor);

    // Also update main settings if they exist
    updateMainLookingForUI(lookingFor);

    // Save to server
    savePreferencesToServer();
}

function updateDropdownLookingForUI(lookingFor) {
    // Update toggle buttons
    const relationshipBtn = document.getElementById('dropdown-toggle-relationship');
    const friendsBtn = document.getElementById('dropdown-toggle-friends');
    const valueDisplay = document.getElementById('dropdown-looking-for-value');

    if (relationshipBtn && friendsBtn) {
        relationshipBtn.classList.toggle('active', lookingFor === 'relationship');
        friendsBtn.classList.toggle('active', lookingFor === 'friends');
    }

    if (valueDisplay) {
        valueDisplay.textContent = lookingFor === 'relationship' ? 'Relationship' : 'Friends';
    }

    // Update instruction text
    updateInstructionText(lookingFor);
}

// Main settings functions
function updateMainGender(gender) {
    localStorage.setItem('userGender', gender);
    updateMainGenderUI(gender);

    // Also update dropdown if it exists
    updateDropdownGenderUI(gender);

    // Save to server
    savePreferencesToServer();
}

function updateMainInterestedIn(interested) {
    localStorage.setItem('interestedIn', interested);
    updateMainInterestedInUI(interested);

    // Also update dropdown if it exists
    updateDropdownInterestedInUI(interested);

    // Save to server
    savePreferencesToServer();
}

function updateMainLookingFor(lookingFor) {
    localStorage.setItem('lookingFor', lookingFor);
    updateMainLookingForUI(lookingFor);

    // Also update dropdown if it exists
    updateDropdownLookingForUI(lookingFor);

    // Save to server
    savePreferencesToServer();
}

// UI update functions for main settings
function updateMainGenderUI(gender) {
    // Update main settings view if it exists
    const mainButtons = document.querySelectorAll('[id^="main-gender-"]');
    mainButtons.forEach(btn => btn.classList.remove('selected'));
    const mainSelected = document.getElementById(`main-gender-${gender}`);
    if (mainSelected) mainSelected.classList.add('selected');
}

function updateMainInterestedInUI(interested) {
    // Update main settings view if it exists
    const mainButtons = document.querySelectorAll('[id^="main-interested-"]');
    mainButtons.forEach(btn => btn.classList.remove('selected'));
    const mainSelected = document.getElementById(`main-interested-${interested}`);
    if (mainSelected) mainSelected.classList.add('selected');
}

function updateMainLookingForUI(lookingFor) {
    // Update main settings view if it exists
    const mainRelationshipBtn = document.getElementById('main-toggle-relationship');
    const mainFriendsBtn = document.getElementById('main-toggle-friends');
    const mainValueDisplay = document.getElementById('main-looking-for-value');

    if (mainRelationshipBtn && mainFriendsBtn) {
        mainRelationshipBtn.classList.toggle('active', lookingFor === 'relationship');
        mainFriendsBtn.classList.toggle('active', lookingFor === 'friends');
    }

    if (mainValueDisplay) {
        mainValueDisplay.textContent = lookingFor === 'relationship' ? 'Relationship' : 'Friends';
    }

    // Update instruction text
    updateInstructionText(lookingFor);
}

// Save preferences to server
async function savePreferencesToServer() {
    try {
        const token = localStorage.getItem('authToken');
        if (!token) return;

        const preferences = {
            gender: localStorage.getItem('userGender') || '',
            interestedIn: localStorage.getItem('interestedIn') || 'both',
            lookingFor: localStorage.getItem('lookingFor') || 'relationship'
        };

        const response = await fetch('/api/user/preferences', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(preferences)
        });

        if (!response.ok) {
            console.error('Failed to save preferences to server:', response.status);
        } else {
            console.log('Preferences saved to server successfully');
        }
    } catch (error) {
        console.error('Error saving preferences to server:', error);
    }
}

// Update instruction text based on looking for preference
function updateInstructionText(lookingFor) {
    const instructionText = document.getElementById('instruction-text');
    if (instructionText) {
        instructionText.textContent = lookingFor === 'friends'
            ? 'Shake to match your BFF! ♡'
            : 'Shake to match your soul! ♡';
    }
}

// Global functions for verification modals
function closeVerificationInstructionModal() {
    if (window.app) {
        window.app.closeVerificationInstructionModal();
    }
}

function takeVerificationPhoto() {
    if (window.app) {
        window.app.takeVerificationPhoto();
    }
}

function closeVerificationPhotoModal() {
    if (window.app) {
        window.app.closeVerificationPhotoModal();
    }
}

function retakeVerificationPhoto() {
    if (window.app) {
        window.app.retakeVerificationPhoto();
    }
}

function submitVerificationPhoto() {
    if (window.app) {
        window.app.submitVerificationPhoto();
    }
}

// Initialize the app when the page loads
window.addEventListener('load', () => {
    window.app = new ShakeMatchApp();
});
