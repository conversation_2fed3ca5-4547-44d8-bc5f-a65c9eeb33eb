// screens/ProfileScreen.js
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,

  TextInput,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  Modal,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { profileStyles, theme } from './ScreensStyles';
// Removed SVG import - using native components instead

// Predefined passion options
const PASSION_OPTIONS = [
  'Travel', 'Music', 'Movies', 'Reading', 'Cooking', 'Fitness', 'Photography', 'Art',
  'Dancing', 'Gaming', 'Sports', 'Nature', 'Technology', 'Fashion', 'Food', 'Coffee',
  'Wine', 'Hiking', 'Beach', 'Yoga', 'Meditation', 'Writing', 'Learning', 'Animals',
  'Cars', 'Motorcycles', 'Cycling', 'Running', 'Swimming', 'Skiing', 'Surfing', 'Climbing',
  'Comedy', 'Theater', 'Concerts', 'Festivals', 'Volunteering', 'Gardening', 'DIY',
  'Shopping', 'Nightlife', 'Adventure', 'Culture', 'History', 'Science', 'Politics'
];

// Hand gesture components using native React Native elements
const HandGesture = ({ fingers, size = 120, color = '#e83333' }) => {
  const getFingerEmoji = (count) => {
    const fingerEmojis = {
      1: '☝️',
      2: '✌️',
      3: '🤟',
      4: '🖖',
      5: '🖐️'
    };
    return fingerEmojis[count] || '☝️';
  };

  return (
    <View style={{
      alignItems: 'center',
      justifyContent: 'center',
      width: size,
      height: size,
      backgroundColor: 'rgba(78, 154, 241, 0.1)',
      borderRadius: size / 2,
      borderWidth: 3,
      borderColor: color,
      borderStyle: 'dashed'
    }}>
      <Text style={{
        fontSize: size * 0.6,
        textAlign: 'center',
        lineHeight: size * 0.7
      }}>
        {getFingerEmoji(fingers)}
      </Text>
    </View>
  );
};

// Enhanced instruction display with face and bigger emoji
const InstructionDisplay = ({ fingers, size = 100 }) => {
  const getFingerEmoji = (count) => {
    const fingerEmojis = {
      1: '☝️',
      2: '✌️',
      3: '🤟',
      4: '🖖',
      5: '🖐️'
    };
    return fingerEmojis[count] || '☝️';
  };

  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      {/* Instruction text */}
      <Text style={{ fontSize: 18, color: '#666', textAlign: 'center', marginBottom: 30 }}>
        Show your face and hold up:
      </Text>

      {/* Hand gesture and face side by side */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
        gap: 30
      }}>
        {/* Big hand gesture - no border */}
        <View style={{
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Text style={{
            fontSize: size * 0.7,
            textAlign: 'center'
          }}>
            {getFingerEmoji(fingers)}
          </Text>
        </View>

        {/* Face on the right */}
        <View style={{
          alignItems: 'center'
        }}>
          <Text style={{ fontSize: 100 }}>😊</Text>
        </View>
      </View>

      {/* Instruction text */}
      <Text style={{
        fontSize: 24,
        fontWeight: '600',
        color: '#e83333',
        textAlign: 'center',
        marginBottom: 10
      }}>
        {fingers} finger{fingers > 1 ? 's' : ''}
      </Text>

      <Text style={{
        fontSize: 16,
        color: '#666',
        textAlign: 'center'
      }}>
        Make sure both your face and fingers are clearly visible
      </Text>
    </View>
  );
};

// Animated verification instruction component
const VerificationAnimation = ({ requiredFingers, style }) => {
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    // Pulse animation
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: fadeAnim,
          transform: [{ scale: pulseAnim }],
          alignItems: 'center',
          justifyContent: 'center',
        }
      ]}
    >
      <HandGesture fingers={requiredFingers} size={150} color="#e83333" />

      {/* Camera frame overlay with corner indicators */}
      <View style={{
        position: 'absolute',
        width: 200,
        height: 200,
        borderWidth: 2,
        borderColor: '#ff4444',
        borderRadius: 20,
        backgroundColor: 'transparent',
      }}>
        {/* Corner brackets */}
        <View style={{ position: 'absolute', top: -2, left: -2, width: 30, height: 30, borderTopWidth: 4, borderLeftWidth: 4, borderColor: '#ff4444', borderTopLeftRadius: 20 }} />
        <View style={{ position: 'absolute', top: -2, right: -2, width: 30, height: 30, borderTopWidth: 4, borderRightWidth: 4, borderColor: '#ff4444', borderTopRightRadius: 20 }} />
        <View style={{ position: 'absolute', bottom: -2, left: -2, width: 30, height: 30, borderBottomWidth: 4, borderLeftWidth: 4, borderColor: '#ff4444', borderBottomLeftRadius: 20 }} />
        <View style={{ position: 'absolute', bottom: -2, right: -2, width: 30, height: 30, borderBottomWidth: 4, borderRightWidth: 4, borderColor: '#ff4444', borderBottomRightRadius: 20 }} />
      </View>
    </Animated.View>
  );
};

const ProfileScreen = ({ navigation, route, user, serverAddress, onUpdateProfile, isInitialSetup = false }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [age, setAge] = useState('');
  const [showAgeModal, setShowAgeModal] = useState(false);
  const [description, setDescription] = useState('');
  const [passions, setPassions] = useState('');
  const [selectedPassions, setSelectedPassions] = useState([]);
  const [images, setImages] = useState(['', '', '', '']);
  const [profileData, setProfileData] = useState(null);
  const [errors, setErrors] = useState({});  // Add state for validation errors

  // Verification-related state
  const [verificationStatus, setVerificationStatus] = useState({
    isVerified: false,
    badgeType: null,
    verifiedAt: null,
    request: null
  });
  const [isLoadingVerification, setIsLoadingVerification] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationPhoto, setVerificationPhoto] = useState(null);
  const [requiredFingers, setRequiredFingers] = useState(null);
  const [showFingerAnimation, setShowFingerAnimation] = useState(false);
  const [showInstructionModal, setShowInstructionModal] = useState(false);
  const [shouldOpenCamera, setShouldOpenCamera] = useState(false);
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Refs for input fields (for focusing next input)
  const descriptionRef = useRef(null);
  const passionsRef = useRef(null);

  // Add effect to handle back button and navigation
  useEffect(() => {
    if (isInitialSetup) {
      // Set custom header options to prevent going back
      navigation.setOptions({
        headerTitle: 'Complete Your Profile',
        headerLeft: () => null,  // Remove back button
        gestureEnabled: false,   // Disable swipe back gesture
      });
    }
  }, [navigation, isInitialSetup]);

  // Fetch user profile data when screen loads
  useEffect(() => {
    fetchProfileData();
  }, []);

  // Request permissions for image library
  useEffect(() => {
    (async () => {
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 
            'Sorry, we need camera roll permissions to upload images.');
        }
      }
    })();
  }, []);

  const fetchProfileData = async () => {
    const userId = getValidUserId();
    if (!userId || !serverAddress) return;

    setLoading(true);
    try {
      // Check if profile is cached
      const cachedProfile = await AsyncStorage.getItem(`profile_${userId}`);
      if (cachedProfile) {
        const parsedProfile = JSON.parse(cachedProfile);
        setProfileData(parsedProfile);
        setAge(parsedProfile.age ? parsedProfile.age.toString() : '');
        setDescription(parsedProfile.description || '');
        setPassions(parsedProfile.passions ? parsedProfile.passions.join(', ') : '');
        setSelectedPassions(parsedProfile.passions || []);
        setImages(
          parsedProfile.images && parsedProfile.images.length > 0 
            ? [...parsedProfile.images, ...Array(4 - parsedProfile.images.length).fill('')] 
            : ['', '', '', '']
        );
      }

      // Fetch from server
      const response = await fetch(`http://${serverAddress}/api/profile/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`Error response from server: ${errorText}`);
        if (response.status === 404) {
          // If user not found, it might be a new user without a profile yet
          // This is not necessarily an error
          setLoading(false);
          return;
        }
        throw new Error(errorText || 'Failed to fetch profile');
      }

      const data = await response.json();
      
      setProfileData(data.profile);
      setAge(data.profile.age ? data.profile.age.toString() : '');
      setDescription(data.profile.description || '');
      setPassions(data.profile.passions ? data.profile.passions.join(', ') : '');
      setSelectedPassions(data.profile.passions || []);
      setImages(
        data.profile.images && data.profile.images.length > 0 
          ? [...data.profile.images, ...Array(4 - data.profile.images.length).fill('')] 
          : ['', '', '', '']
      );

      // Cache the profile data
      await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(data.profile));
    } catch (error) {
      console.error('Error fetching profile:', error);
      // Don't show an alert for 404 errors since this could be a new user
      if (!error.message.includes('not found')) {
        Alert.alert('Error', 'Failed to load profile data. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleImagePick = async (startIndex) => {
    try {
      // Count available empty slots starting from the clicked index
      const emptySlots = [];
      for (let i = startIndex; i < 4; i++) {
        if (images[i] === '') {
          emptySlots.push(i);
        }
      }

      // If no empty slots from this position, just replace the clicked slot
      if (emptySlots.length === 0) {
        emptySlots.push(startIndex);
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        selectionLimit: emptySlots.length, // Limit to available slots
        aspect: [1, 1],
        quality: 0.2,
        base64: true,
        exif: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newImages = [...images];

        // Fill the available slots with selected images
        result.assets.forEach((asset, assetIndex) => {
          if (assetIndex < emptySlots.length) {
            const slotIndex = emptySlots[assetIndex];
            newImages[slotIndex] = asset.base64;
          }
        });

        setImages(newImages);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };



  const handleRemoveImage = (index) => {
    const newImages = [...images];

    // Remove the image at the specified index
    newImages[index] = '';

    // Shift all images forward to fill gaps
    const compactedImages = [];

    // First, collect all non-empty images
    for (let i = 0; i < newImages.length; i++) {
      if (newImages[i] !== '') {
        compactedImages.push(newImages[i]);
      }
    }

    // Fill the array with compacted images and empty slots at the end
    const finalImages = ['', '', '', ''];
    for (let i = 0; i < compactedImages.length; i++) {
      finalImages[i] = compactedImages[i];
    }

    setImages(finalImages);
  };

  // Handle passion selection with constraints
  const togglePassion = (passion) => {
    setSelectedPassions(prev => {
      if (prev.includes(passion)) {
        // Removing a passion - check minimum constraint
        if (prev.length <= 3) {
          Alert.alert(
            "Minimum Required",
            "Please select at least 3 passions to continue.",
            [{ text: "OK" }]
          );
          return prev; // Don't remove if at minimum
        }
        return prev.filter(p => p !== passion);
      } else {
        // Adding a passion - check maximum constraint
        if (prev.length >= 6) {
          Alert.alert(
            "Maximum Reached",
            "You can select up to 6 passions. Please remove one to add another.",
            [{ text: "OK" }]
          );
          return prev; // Don't add if at maximum
        }
        return [...prev, passion];
      }
    });
  };

  // Handle photo reordering
  const movePhoto = (fromIndex, toIndex) => {
    if (toIndex < 0 || toIndex >= 4 || fromIndex === toIndex) return;

    const newImages = [...images];
    const temp = newImages[fromIndex];
    newImages[fromIndex] = newImages[toIndex];
    newImages[toIndex] = temp;
    setImages(newImages);
  };

  // Debug function to validate user ID before using it
  const getValidUserId = () => {
    if (!user) {
      console.log('User object is null or undefined');
      return null;
    }
    
    if (!user.id) {
      console.log('User ID is missing in the user object', user);
      return null;
    }
    
    // Check if the ID looks like a valid MongoDB ObjectId
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(user.id);
    console.log(`User ID: ${user.id}, valid ObjectId format: ${isValidObjectId}`);
    
    return user.id;
  };

  // Add this function to validate the profile
  const validateProfile = () => {
    const newErrors = {};

    if (!description.trim()) {
      newErrors.description = 'Please add a description about yourself';
    }

    if (!age) {
        newErrors.age = 'Age is required';
      } else {
        const ageNum = parseInt(age);
        if (isNaN(ageNum) || ageNum < 18 || ageNum > 120) {
          newErrors.age = 'Please enter a valid age between 18 and 120';
        }
      }

    // Check passion selection constraints
    if (selectedPassions.length < 3) {
      newErrors.passions = `Please select at least 3 passions (${selectedPassions.length}/3 selected)`;
    } else if (selectedPassions.length > 6) {
      newErrors.passions = `Please select no more than 6 passions (${selectedPassions.length}/6 selected)`;
    }

    // Check for minimum of 3 pictures
    if (isInitialSetup) {
      const photoCount = images.filter(img => img !== '').length;
      if (photoCount < 3) {
        newErrors.photos = `Please add at least 3 photos (${photoCount}/3 uploaded)`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveProfile = async () => {
    // Dismiss keyboard
    Keyboard.dismiss();
    
    const userId = getValidUserId();
    if (!userId || !serverAddress) {
      Alert.alert('Error', 'Invalid user ID or server address. Please try logging in again.');
      return;
    }

    // Validate profile data
    if (!validateProfile()) {
      // If there are validation errors and this is initial setup, show an alert
      if (isInitialSetup) {
        Alert.alert(
          'Incomplete Profile',
          'Please complete your profile before continuing. A description about yourself is required.',
          [{ text: 'OK' }]
        );
      }
      return;
    }

    setSaving(true);
    try {
      // Filter out empty images and ensure proper format
      const filteredImages = images
        .filter(img => img !== '')
        .map(img => {
          // If it's already a data URI, extract just the base64 part
          if (img.startsWith('data:image')) {
            return img.split(',')[1];
          }
          // Return as is if it's just the base64 string
          return img;
        });
      
      // Check if images are too large
      const totalImagesSize = JSON.stringify(filteredImages).length;
      if (totalImagesSize > 5000000) { // ~5MB limit
        Alert.alert(
          'Images Too Large', 
          'Your images are too large. Please try using fewer or smaller images.',
          [{ text: 'OK' }]
        );
        setSaving(false);
        return;
      }
      
      const profileData = {
        age: age ? parseInt(age) : null,
        description,
        passions: selectedPassions,
        images: filteredImages,
      };

      console.log(`Saving profile for user ID: ${userId}`);
      
      const response = await fetch(`http://${serverAddress}/api/profile/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`Error response from server: ${errorText}`);
        throw new Error(errorText || 'Failed to update profile');
      }
      
      const data = await response.json().catch(e => {
        console.log('Error parsing JSON response:', e);
        return { profile: profileData };
      });

      // Cache the updated profile data
      await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(data.profile || profileData));
      
      // Update app state if callback provided
      if (onUpdateProfile) {
        onUpdateProfile(data.profile || profileData);
      }

      if (isInitialSetup) {
        // Show alert and then navigate to Home screen
        Alert.alert(
          'Profile Completed',
          'Your profile has been set up. You can now use the app!',
          [
            { 
              text: 'Continue', 
              onPress: () => {
                // Explicitly navigate to Home screen after profile setup
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Home' }],
                });
              }
            }
          ]
        );
      } else {
        Alert.alert('Success', 'Your profile has been updated successfully.');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', `Failed to save profile: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  // ===== VERIFICATION FUNCTIONS =====

  // Load verification status
  const loadVerificationStatus = async () => {
    if (!user || !serverAddress) {
      return;
    }

    setIsLoadingVerification(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }

      if (!token) {
        return;
      }

      const url = `http://${serverAddress}/api/verification/status`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setVerificationStatus(data);
      } else {
        console.log('Verification status error:', response.status);
        setVerificationStatus({
          isVerified: false,
          badgeType: null,
          verifiedAt: null,
          request: null
        });
      }
    } catch (error) {
      console.error('Error loading verification status:', error);
      setVerificationStatus({
        isVerified: false,
        badgeType: null,
        verifiedAt: null,
        request: null
      });
    } finally {
      setIsLoadingVerification(false);
    }
  };

  // Start pulse animation
  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // Direct camera function that bypasses modal issues
  const openCameraDirect = async () => {
    console.log('� openCameraDirect called');
    try {
      // Request permissions first
      console.log('🔐 Requesting camera permissions...');
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      console.log('🔐 Camera permission status:', status);

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is required to take verification photos.');
        return;
      }

      console.log('📸 Opening native camera directly...');

      // Try the most basic camera call possible
      const result = await ImagePicker.launchCameraAsync({
        quality: 0.8,
        base64: true, // Ensure we get base64 data for admin panel
      });

      console.log('📸 Direct camera result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        console.log('✅ Direct camera photo taken successfully');
        const asset = result.assets[0];
        // Use base64 if available, otherwise use URI
        const photoData = asset.base64
          ? `data:image/jpeg;base64,${asset.base64}`
          : asset.uri;
        console.log('📸 Photo data format:', photoData.startsWith('data:') ? 'base64' : 'URI');
        setVerificationPhoto(photoData);
        setShowVerificationModal(true);
      } else {
        console.log('� Camera was canceled or failed');
      }
    } catch (error) {
      console.error('❌ Direct camera error:', error);
      Alert.alert('Camera Error', `Failed to open camera: ${error.message}`);
    }
  };

  // Take verification photo with camera
  const takeVerificationPhoto = async () => {
    console.log('🎯 takeVerificationPhoto called');
    try {
      // Generate random number of fingers (1-5)
      const randomFingers = Math.floor(Math.random() * 5) + 1;
      console.log('🤚 Required fingers:', randomFingers);
      setRequiredFingers(randomFingers);

      // Show instruction modal first
      console.log('📱 Showing instruction modal');
      setShowInstructionModal(true);

    } catch (error) {
      console.error('❌ Error preparing verification photo:', error);
      Alert.alert('Error', 'Failed to prepare camera. Please try again.');
    }
  };



  // Open camera for verification photo
  const openCamera = async () => {
    console.log('🎥 openCamera called');
    try {
      if (Platform.OS === 'web') {
        console.log('📱 Web platform detected');
        // For web browsers, use HTML5 camera input
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.capture = 'environment'; // Use rear camera if available

        input.onchange = (event) => {
          const file = event.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
              const base64Image = e.target.result;
              setVerificationPhoto(base64Image);
              setShowVerificationModal(true);
            };
            reader.readAsDataURL(file);
          }
        };

        input.click();
      } else {
        console.log('📱 Mobile platform detected');
        // For mobile (iOS/Android)
        console.log('🔐 Requesting camera permissions...');
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        console.log('🔐 Camera permission status:', status);

        if (status !== 'granted') {
          console.log('❌ Camera permission denied');
          Alert.alert('Permission Required', 'Sorry, we need camera permissions to take verification photos.');
          return;
        }

        console.log('📸 Launching camera...');
        console.log('📸 ImagePicker object:', ImagePicker);
        console.log('📸 launchCameraAsync function:', typeof ImagePicker.launchCameraAsync);

        // Launch camera with timeout
        console.log('📸 Using image library as workaround for camera issue...');

        // Since launchCameraAsync hangs on iOS, use image library instead
        // This will give user option to take photo or choose from library
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
          base64: true,
        });

        console.log('📸 Image library result received');

        console.log('📸 Camera result received:', result);
        console.log('📸 Result type:', typeof result);
        console.log('📸 Result keys:', Object.keys(result));

        if (!result.canceled && result.assets && result.assets[0]) {
          console.log('✅ Photo taken successfully');
          const asset = result.assets[0];
          const base64Image = `data:image/jpeg;base64,${asset.base64}`;
          setVerificationPhoto(base64Image);
          setShowVerificationModal(true);
        } else {
          console.log('❌ Camera was canceled or no photo taken');
        }
      }
    } catch (error) {
      console.error('❌ Error taking verification photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  // Convert image URI to base64
  const convertImageToBase64 = async (uri) => {
    try {
      if (uri.startsWith('data:')) {
        // Already base64
        console.log('📸 Photo is already base64 format');
        return uri;
      }

      console.log('📸 Converting URI to base64:', uri.substring(0, 50) + '...');

      if (Platform.OS === 'web') {
        // For web, use fetch and FileReader
        const response = await fetch(uri);
        const blob = await response.blob();

        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      } else {
        // For mobile, use expo-file-system if available, or fetch
        try {
          const response = await fetch(uri);
          const blob = await response.blob();

          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
        } catch (fetchError) {
          console.error('Fetch failed, trying alternative method:', fetchError);
          // Fallback: return the URI as is and let the server handle it
          return uri;
        }
      }
    } catch (error) {
      console.error('Error converting image to base64:', error);
      // Fallback: return the original URI
      return uri;
    }
  };

  // Submit verification request
  const submitVerificationRequest = async () => {
    if (!verificationPhoto) {
      Alert.alert('Error', 'Please take a verification photo first.');
      return;
    }

    setIsLoadingVerification(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }

      if (!token) {
        Alert.alert('Error', 'Please log in again.');
        return;
      }

      console.log('📸 Converting verification photo to base64...');
      // Convert photo to base64 for admin panel viewing
      const base64Photo = await convertImageToBase64(verificationPhoto);
      console.log('📸 Photo converted to base64, length:', base64Photo.length);

      const url = `http://${serverAddress}/api/verification/request`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          verificationPhoto: base64Photo,
          requiredFingers: requiredFingers || Math.floor(Math.random() * 5) + 1 // Fallback if not set
        }),
      });

      if (response.ok) {
        const data = await response.json();
        Alert.alert('Success', 'Verification request submitted successfully! We will review it within 24-48 hours.');
        setShowVerificationModal(false);
        setVerificationPhoto(null);
        setRequiredFingers(null);
        loadVerificationStatus(); // Refresh status
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to submit verification request');
      }
    } catch (error) {
      console.error('Error submitting verification request:', error);
      Alert.alert('Error', 'Failed to submit verification request. Please try again.');
    } finally {
      setIsLoadingVerification(false);
    }
  };

  // Render hand gesture based on finger count
  const renderFingerDisplay = (count) => {
    if (!count) return null;

    return (
      <View style={profileStyles.fingerDisplayContainer}>
        <HandGesture fingers={count} size={120} color="#e83333" />
      </View>
    );
  };

  // Load verification status on mount
  useEffect(() => {
    if (user && serverAddress) {
      loadVerificationStatus();
    }
  }, [user, serverAddress]);

  // Handle camera opening after modal closes
  useEffect(() => {
    if (shouldOpenCamera && !showInstructionModal) {
      console.log('🎯 Modal closed, opening camera now...');
      setShouldOpenCamera(false);
      // Small delay to ensure UI is stable
      setTimeout(() => {
        openCameraDirect();
      }, 100);
    }
  }, [shouldOpenCamera, showInstructionModal]);

  if (loading) {
    return (
      <View style={profileStyles.loadingContainer}>
        <ActivityIndicator size="large" color="#e83333" />
        <Text style={profileStyles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={profileStyles.keyboardAvoidingView}
      behavior={Platform.OS === 'ios' ? 'padding' : null}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView 
          style={profileStyles.container}
          contentContainerStyle={profileStyles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
        >
          {isInitialSetup && (
            <View style={profileStyles.header}>
              <Text style={profileStyles.requiredNote}>
                * Description, age, and at least 3 photos are required
              </Text>
            </View>
          )}

          <View style={profileStyles.section}>
            <Text style={profileStyles.sectionTitle}>Profile Photos</Text>
            <Text style={profileStyles.sectionSubtitle}>
              {isInitialSetup
                ? <Text>Add at least 3 photos <Text style={profileStyles.requiredStar}>*</Text></Text>
                : 'Add up to 4 photos'}
            </Text>
            {errors.photos && <Text style={profileStyles.errorText}>{errors.photos}</Text>}


            <View style={profileStyles.imagesContainer}>
              {images.map((image, index) => (
                <View key={index} style={profileStyles.imageBox}>
                  {image ? (
                    <View style={profileStyles.imageWrapper}>
                      <Image
                        source={{ uri: image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}` }}
                        style={profileStyles.image}
                      />

                      {/* Reorder buttons */}
                      <View style={profileStyles.reorderButtons}>
                        {index > 0 && images[index - 1] && (
                          <TouchableOpacity
                            style={[profileStyles.reorderButton, profileStyles.reorderButtonLeft]}
                            onPress={() => movePhoto(index, index - 1)}
                          >
                            <Ionicons name="chevron-back" size={16} color="#fff" />
                          </TouchableOpacity>
                        )}
                        {index < 3 && images[index + 1] && (
                          <TouchableOpacity
                            style={[profileStyles.reorderButton, profileStyles.reorderButtonRight]}
                            onPress={() => movePhoto(index, index + 1)}
                          >
                            <Ionicons name="chevron-forward" size={16} color="#fff" />
                          </TouchableOpacity>
                        )}
                      </View>

                      <TouchableOpacity
                        style={profileStyles.removeButton}
                        onPress={() => handleRemoveImage(index)}
                      >
                        <Ionicons name="close-circle" size={24} color="#333" />
                      </TouchableOpacity>

                      {/* Photo number indicator */}
                      <View style={profileStyles.photoNumber}>
                        <Text style={profileStyles.photoNumberText}>{index + 1}</Text>
                      </View>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={profileStyles.addImageButton}
                      onPress={() => handleImagePick(index)}
                    >
                      <Ionicons name="add" size={40} color="#e83333" />
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>

          {/* Verification Section */}
          <View style={profileStyles.section}>
            <View style={profileStyles.verificationHeader}>
              <Text style={profileStyles.sectionTitle}>Account Verification</Text>
              <TouchableOpacity
                style={profileStyles.refreshButton}
                onPress={loadVerificationStatus}
                disabled={isLoadingVerification}
              >
                <Ionicons
                  name="refresh"
                  size={20}
                  color={isLoadingVerification ? "#ccc" : "#e83333"}
                />
              </TouchableOpacity>
            </View>

            {verificationStatus.isVerified ? (
              <View style={profileStyles.verifiedContainer}>
                <View style={profileStyles.verifiedHeader}>
                  <Ionicons
                    name="checkmark-circle"
                    size={24}
                    color={verificationStatus.badgeType === 'gold' ? '#FFD700' : '#e83333'}
                  />
                  <Text style={profileStyles.verifiedText}>Account Verified</Text>
                  {verificationStatus.badgeType && (
                    <View style={[
                      profileStyles.verificationBadge,
                      verificationStatus.badgeType === 'gold' ? profileStyles.goldBadge : profileStyles.blueBadge
                    ]}>
                      <Text style={[
                        profileStyles.badgeText,
                        verificationStatus.badgeType === 'gold' ? profileStyles.goldText : profileStyles.blueText
                      ]}>
                        {verificationStatus.badgeType === 'gold' ? 'GOLD' : 'VERIFIED'}
                      </Text>
                    </View>
                  )}
                </View>
                <Text style={profileStyles.verifiedDescription}>
                  Your account has been verified on {new Date(verificationStatus.verifiedAt).toLocaleDateString()}
                </Text>
              </View>
            ) : (
              <View style={profileStyles.verificationContainer}>
                {verificationStatus.request ? (
                  <>
                    {verificationStatus.request.status === 'rejected' ? (
                      <View style={profileStyles.rejectionContainer}>
                        <View style={profileStyles.rejectionHeader}>
                          <View style={profileStyles.rejectionIconContainer}>
                            <Ionicons name="alert-circle" size={28} color="#FF6B6B" />
                          </View>
                          <Text style={profileStyles.rejectionTitle}>Verification Declined</Text>
                        </View>

                        <Text style={profileStyles.rejectionReasonLabel}>REASON:</Text>
                        <Text style={profileStyles.rejectionReasonText}>
                          {(() => {
                            const rejectionMessages = {
                              'photo_unclear': 'Photo quality needs improvement',
                              'photo_inappropriate': 'Photo content not suitable',
                              'identity_mismatch': 'Photo doesn\'t match your profile',
                              'fake_document': 'Document appears altered',
                              'other': 'Please review requirements'
                            };
                            return rejectionMessages[verificationStatus.request.rejectionReason] || 'Please try again';
                          })()}
                        </Text>

                        <TouchableOpacity
                          style={profileStyles.newRetryButton}
                          onPress={takeVerificationPhoto}
                          disabled={isLoadingVerification}
                        >
                          <Ionicons name="camera-outline" size={22} color="#fff" />
                          <Text style={profileStyles.newRetryButtonText}>Take New Photo</Text>
                        </TouchableOpacity>
                      </View>
                    ) : (
                      <View style={profileStyles.requestStatusContainer}>
                        <View style={profileStyles.requestHeader}>
                          <Ionicons
                            name={verificationStatus.request.status === 'pending' ? 'time' : 'checkmark-circle'}
                            size={24}
                            color={verificationStatus.request.status === 'pending' ? '#FF9500' : '#34C759'}
                          />
                          <Text style={profileStyles.requestStatusText}>
                            Request {verificationStatus.request.status === 'pending' ? 'Pending' : 'Approved'}
                          </Text>
                        </View>
                        <Text style={profileStyles.requestDescription}>
                          {verificationStatus.request.status === 'pending'
                            ? 'Your verification request is being reviewed. This usually takes 24-48 hours.'
                            : 'Your verification request was approved!'}
                        </Text>
                      </View>
                    )}
                  </>
                ) : (
                  <View style={profileStyles.notVerifiedContainer}>
                    <View style={profileStyles.verificationIconContainer}>
                      <Ionicons name="shield-checkmark-outline" size={48} color="#e83333" />
                    </View>
                    <TouchableOpacity
                      style={profileStyles.verifyButton}
                      onPress={takeVerificationPhoto}
                      disabled={isLoadingVerification}
                    >
                      <Ionicons
                        name={isLoadingVerification ? "hourglass" : "camera"}
                        size={20}
                        color="#fff"
                      />
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            )}
          </View>



          <View style={profileStyles.section}>
            <Text style={profileStyles.sectionTitle}>About You</Text>
            
            <View style={profileStyles.formGroup}>
              <Text style={profileStyles.label}>
                Age {<Text style={profileStyles.requiredStar}>*</Text>}
              </Text>
              <TouchableOpacity
                style={[profileStyles.input, profileStyles.dropdownInput, errors.age && profileStyles.inputError]}
                onPress={() => setShowAgeModal(true)}
              >
                <Text style={[profileStyles.dropdownText, !age && profileStyles.placeholderText]}>
                  {age || 'Select your age'}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#666" />
              </TouchableOpacity>
              {errors.age && <Text style={profileStyles.errorText}>{errors.age}</Text>}
            </View>
            
            <View style={profileStyles.formGroup}>
              <Text style={profileStyles.label}>
                Description <Text style={profileStyles.requiredStar}>*</Text>
              </Text>
              <TextInput
                ref={descriptionRef}
                style={[profileStyles.input, profileStyles.textArea, errors.description && profileStyles.inputError]}
                placeholder="Share a little about yourself..."
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={4}
                maxLength={500}
                returnKeyType="next"
                onSubmitEditing={() => passionsRef.current && passionsRef.current.focus()}
                blurOnSubmit={false}
                autoCorrect={false}
                autoCompleteType="off"
                textContentType="none"
                spellCheck={false}
              />
              <Text style={profileStyles.charCount}>{description.length}/500</Text>
              {errors.description && <Text style={profileStyles.errorText}>{errors.description}</Text>}
            </View>
            
            <View style={profileStyles.formGroup}>
              <Text style={profileStyles.label}>
                Passions <Text style={profileStyles.requiredStar}>*</Text>
              </Text>
              <Text style={profileStyles.helperText}>
                Select 3-6 interests that represent you.
              </Text>
              {errors.passions && <Text style={profileStyles.errorText}>{errors.passions}</Text>}

              <View style={profileStyles.passionContainer}>
                {PASSION_OPTIONS.map((passion) => (
                  <TouchableOpacity
                    key={passion}
                    style={[
                      profileStyles.passionTag,
                      selectedPassions.includes(passion) && profileStyles.selectedPassionTag,
                      selectedPassions.length >= 6 && !selectedPassions.includes(passion) && profileStyles.disabledPassionTag
                    ]}
                    onPress={() => togglePassion(passion)}
                    disabled={selectedPassions.length >= 6 && !selectedPassions.includes(passion)}
                  >
                    <Text style={[
                      profileStyles.passionTagText,
                      selectedPassions.includes(passion) && profileStyles.selectedPassionTagText,
                      selectedPassions.length >= 6 && !selectedPassions.includes(passion) && profileStyles.disabledPassionTagText
                    ]}>
                      {passion}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={profileStyles.passionStatus}>
                <Text style={[
                  profileStyles.selectedCount,
                  selectedPassions.length < 3 && profileStyles.warningText,
                  selectedPassions.length >= 3 && selectedPassions.length <= 6 && profileStyles.successText
                ]}>
                  {selectedPassions.length}/6 passions selected
                  {selectedPassions.length < 3 && ` (need ${3 - selectedPassions.length} more)`}
                </Text>
              </View>
            </View>
          </View>

          <View style={profileStyles.buttonContainer}>
            <TouchableOpacity
              style={profileStyles.saveButton}
              onPress={handleSaveProfile}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={profileStyles.saveButtonText}>
                  {isInitialSetup ? 'Complete Profile' : 'Save Profile'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
          
          {/* Add padding at the bottom for iOS keyboard */}
          {Platform.OS === 'ios' && <View style={profileStyles.keyboardPadding} />}
        </ScrollView>
      </TouchableWithoutFeedback>

      {/* Age Selection Modal */}
      <Modal
        visible={showAgeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAgeModal(false)}
      >
        <View style={profileStyles.modalOverlay}>
          <View style={profileStyles.modalContent}>
            <View style={profileStyles.modalHeader}>
              <Text style={profileStyles.modalTitle}>Select Your Age</Text>
              <TouchableOpacity
                onPress={() => setShowAgeModal(false)}
                style={profileStyles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={profileStyles.ageList} showsVerticalScrollIndicator={false}>
              {Array.from({ length: 103 }, (_, i) => i + 18).map((ageOption) => (
                <TouchableOpacity
                  key={ageOption}
                  style={[
                    profileStyles.ageOption,
                    age === ageOption.toString() && profileStyles.selectedAgeOption
                  ]}
                  onPress={() => {
                    setAge(ageOption.toString());
                    setShowAgeModal(false);
                  }}
                >
                  <Text style={[
                    profileStyles.ageOptionText,
                    age === ageOption.toString() && profileStyles.selectedAgeOptionText
                  ]}>
                    {ageOption}
                  </Text>
                  {age === ageOption.toString() && (
                    <Ionicons name="checkmark" size={20} color="#e83333" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Instruction Modal */}
      <Modal
        visible={showInstructionModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowInstructionModal(false)}
      >
        <View style={profileStyles.instructionModalOverlay}>
          <View style={profileStyles.instructionModalContent}>
            <View style={profileStyles.instructionModalHeader}>
              <Text style={profileStyles.instructionModalTitle}>Verification Photo</Text>
              <TouchableOpacity
                onPress={() => setShowInstructionModal(false)}
                style={profileStyles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <View style={profileStyles.instructionContent}>
              <InstructionDisplay fingers={requiredFingers} size={200} />
            </View>

            <View style={profileStyles.instructionButtons}>
              <TouchableOpacity
                style={profileStyles.cancelButton}
                onPress={() => setShowInstructionModal(false)}
              >
                <Text style={profileStyles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={profileStyles.takePhotoButton}
                onPress={() => {
                  console.log('🎯 Take Photo button pressed');
                  console.log('🎯 Closing modal and using Alert approach...');
                  setShowInstructionModal(false);

                  // Use Alert approach which might work better
                  setTimeout(() => {
                    Alert.alert(
                      'Take Verification Photo',
                      `Hold up ${requiredFingers} finger${requiredFingers > 1 ? 's' : ''} and take a selfie`,
                      [
                        { text: 'Cancel', style: 'cancel' },
                        {
                          text: 'Open Camera',
                          onPress: async () => {
                            try {
                              console.log('📸 Alert camera button pressed');

                              const { status } = await ImagePicker.requestCameraPermissionsAsync();
                              if (status !== 'granted') {
                                Alert.alert('Permission Required', 'Camera permission is required');
                                return;
                              }

                              console.log('📸 Launching camera from Alert...');
                              const result = await ImagePicker.launchCameraAsync({
                                quality: 0.8,
                                base64: true, // Ensure we get base64 data
                              });

                              console.log('📸 Alert camera result:', result);

                              if (!result.canceled && result.assets && result.assets[0]) {
                                console.log('✅ Alert camera success');
                                const asset = result.assets[0];
                                // Use base64 if available, otherwise use URI
                                const photoData = asset.base64
                                  ? `data:image/jpeg;base64,${asset.base64}`
                                  : asset.uri;
                                console.log('📸 Photo data format:', photoData.startsWith('data:') ? 'base64' : 'URI');
                                setVerificationPhoto(photoData);
                                setShowVerificationModal(true);
                              }
                            } catch (error) {
                              console.error('❌ Alert camera error:', error);
                              Alert.alert('Error', `Camera failed: ${error.message}`);
                            }
                          }
                        }
                      ]
                    );
                  }, 300);
                }}
              >
                <Ionicons name="camera" size={20} color="#fff" style={{ marginRight: 8 }} />
                <Text style={profileStyles.takePhotoButtonText}>Take Photo</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Finger Animation Modal */}
      <Modal
        visible={showFingerAnimation}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowFingerAnimation(false)}
      >
        <View style={profileStyles.fingerAnimationOverlay}>
          <View style={profileStyles.fingerAnimationContainer}>
            <Animated.View style={[profileStyles.fingerDisplay, { transform: [{ scale: pulseAnim }] }]}>
              {renderFingerDisplay(requiredFingers)}
            </Animated.View>
            <View style={profileStyles.cameraIcon}>
              <Ionicons name="camera" size={40} color="#e83333" />
            </View>
          </View>
        </View>
      </Modal>

      {/* Verification Photo Modal */}
      <Modal
        visible={showVerificationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowVerificationModal(false)}
      >
        <View style={profileStyles.verificationModalContainer}>
          <View style={profileStyles.verificationModalHeader}>
            <TouchableOpacity
              style={profileStyles.modalCloseButton}
              onPress={() => setShowVerificationModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            <View style={profileStyles.headerCenter}>
              <Ionicons name="shield-checkmark" size={24} color="#e83333" />
            </View>
            <TouchableOpacity
              style={[profileStyles.submitButton, { opacity: verificationPhoto ? 1 : 0.5 }]}
              onPress={submitVerificationRequest}
              disabled={!verificationPhoto || isLoadingVerification}
            >
              <Ionicons
                name={isLoadingVerification ? "hourglass" : "checkmark"}
                size={16}
                color="#fff"
              />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={profileStyles.verificationModalContent}
            contentContainerStyle={profileStyles.verificationModalScrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Visual finger requirement display */}
            <View style={profileStyles.fingerRequirementDisplay}>
          
              {requiredFingers && (
                <Text style={profileStyles.fingerInstructionText}>
                  {(() => {
                    const fingerEmojis = { 1: '☝️', 2: '✌️', 3: '🤟', 4: '🖖', 5: '🖐️' };
                    return fingerEmojis[requiredFingers] || '☝️';
                  })()} Hold up {requiredFingers} finger{requiredFingers > 1 ? 's' : ''}
                </Text>
              )}
            </View>

            {verificationPhoto && (
              <View style={profileStyles.photoPreviewContainer}>
                <Image source={{ uri: verificationPhoto }} style={profileStyles.photoPreview} />

              </View>
            )}

            {/* Tips with icons and text */}
            <View style={profileStyles.verificationTips}>
              <Text style={profileStyles.tipsTitle}>Tips for verification:</Text>
              <View style={profileStyles.tipRow}>
                <Ionicons name="sunny" size={16} color="#FFD700" />
                <Text style={profileStyles.tipText}>Good lighting</Text>
              </View>
              <View style={profileStyles.tipRow}>
                <Ionicons name="eye" size={16} color="#e83333" />
                <Text style={profileStyles.tipText}>Face clearly visible</Text>
              </View>
              <View style={profileStyles.tipRow}>
                <Ionicons name="checkmark-circle" size={16} color="#34C759" />
                <Text style={profileStyles.tipText}>Hold steady</Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
};

// Styles are now imported from ScreensStyles.js

export default ProfileScreen;

